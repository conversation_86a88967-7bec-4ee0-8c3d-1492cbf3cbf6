 
{"timestamp": 1755011956.953664, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "starting worker", "taskName": "agent_runner", "version": "1.0.23", "rtc-version": "1.0.8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011956.955249, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 403, "message": "preloading plugins", "taskName": "agent_runner", "packages": ["livekit.plugins.elevenlabs", "livekit.plugins.openai", "livekit.plugins.silero", "livekit.plugins.deepgram", "livekit.plugins.google"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011959.1286561, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-3", "pid": 58, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011959.1313152, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-4", "pid": 60, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011959.135483, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-6", "pid": 62, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011959.1390917, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-5", "pid": 64, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011959.1464236, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-7", "pid": 66, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011959.1487565, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-9", "pid": 68, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011959.1508458, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-8", "pid": 70, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011959.1551833, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-10", "pid": 72, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011959.1621888, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-11", "pid": 74, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011959.166801, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-12", "pid": 76, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011959.1706886, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-14", "pid": 78, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011959.184792, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-13", "pid": 80, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.695342, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-5", "pid": 64, "elapsed_time": 1.55, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.6952832, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.7003922, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-7", "pid": 66, "elapsed_time": 1.55, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.70027, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.7141798, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-3", "pid": 58, "elapsed_time": 1.58, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.714194, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.7369378, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-9", "pid": 68, "elapsed_time": 1.59, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.7369301, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.7381742, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.7407305, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-4", "pid": 60, "elapsed_time": 1.61, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.772819, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-10", "pid": 72, "elapsed_time": 1.62, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.7727983, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.7729063, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.7733305, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.773279, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.7741244, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-6", "pid": 62, "elapsed_time": 1.64, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.775819, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-14", "pid": 78, "elapsed_time": 1.6, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.7769794, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-8", "pid": 70, "elapsed_time": 1.63, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.8224936, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-12", "pid": 76, "elapsed_time": 1.65, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.8224928, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.822464, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.8239799, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-13", "pid": 80, "elapsed_time": 1.64, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.8591094, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-11", "pid": 74, "elapsed_time": 1.7, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.859061, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011960.8605416, "level": "INFO", "logger": "livekit.agents", "module": "_run", "function": "_worker_started", "line": 69, "message": "see tracing information at http://localhost:8081/debug", "taskName": "agent_runner", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011961.9963198, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 805, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_2yvBMQ3rMzEF", "url": "wss://salon-ai-p43dr7ww.livekit.cloud", "region": "US West B", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011975.4980526, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 28, "message": "Test logging setup started", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011975.4980526, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test shutdown in livekit", "taskName": null, "event": "shutdown", "component": "livekit", "status": "in_progress", "duration_ms": 30, "user_id": "test_user_9", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011976.011897, "level": "ERROR", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in database", "taskName": null, "event": "shutdown", "component": "database", "status": "error", "error_type": "TestError", "error_code": 4806, "retry_count": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011976.5131247, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test shutdown in livekit", "taskName": null, "event": "shutdown", "component": "livekit", "status": "in_progress", "duration_ms": 450, "user_id": "test_user_75", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011977.014143, "level": "INFO", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test shutdown in metrics", "taskName": null, "event": "shutdown", "component": "metrics", "status": "in_progress", "duration_ms": 942, "user_id": "test_user_73", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011977.515134, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test performance_check in livekit", "taskName": null, "event": "performance_check", "component": "livekit", "status": "in_progress", "duration_ms": 955, "user_id": "test_user_26", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011978.017005, "level": "INFO", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test user_action in database", "taskName": null, "event": "user_action", "component": "database", "status": "in_progress", "duration_ms": 402, "user_id": "test_user_72", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011978.5181708, "level": "WARNING", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in voice_agent", "taskName": null, "event": "error_recovery", "component": "voice_agent", "status": "warning", "threshold_value": 75, "current_value": 99, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011979.018705, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test user_action in voice_agent", "taskName": null, "event": "user_action", "component": "voice_agent", "status": "success", "duration_ms": 670, "user_id": "test_user_52", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011979.5199282, "level": "ERROR", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in voice_agent", "taskName": null, "event": "performance_check", "component": "voice_agent", "status": "error", "error_type": "TestError", "error_code": 1332, "retry_count": 3, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011980.0207834, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test performance_check in voice_agent", "taskName": null, "event": "performance_check", "component": "voice_agent", "status": "success", "duration_ms": 655, "user_id": "test_user_35", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011980.521823, "level": "WARNING", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in voice_agent", "taskName": null, "event": "performance_check", "component": "voice_agent", "status": "warning", "threshold_value": 96, "current_value": 93, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011981.0231576, "level": "WARNING", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in metrics", "taskName": null, "event": "performance_check", "component": "metrics", "status": "warning", "threshold_value": 94, "current_value": 87, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011981.5258002, "level": "WARNING", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in livekit", "taskName": null, "event": "startup", "component": "livekit", "status": "warning", "threshold_value": 83, "current_value": 110, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011982.0273342, "level": "INFO", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test user_action in database", "taskName": null, "event": "user_action", "component": "database", "status": "success", "duration_ms": 936, "user_id": "test_user_49", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011982.5282538, "level": "WARNING", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 60, "message": "Test warning in livekit", "taskName": null, "event": "startup", "component": "livekit", "status": "warning", "threshold_value": 76, "current_value": 109, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011983.0290954, "level": "INFO", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test performance_check in metrics", "taskName": null, "event": "performance_check", "component": "metrics", "status": "success", "duration_ms": 813, "user_id": "test_user_60", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011983.5301604, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test shutdown in voice_agent", "taskName": null, "event": "shutdown", "component": "voice_agent", "status": "in_progress", "duration_ms": 127, "user_id": "test_user_23", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011984.0310113, "level": "ERROR", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in voice_agent", "taskName": null, "event": "user_action", "component": "voice_agent", "status": "error", "error_type": "TestError", "error_code": 2031, "retry_count": 1, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011984.5317018, "level": "INFO", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 68, "message": "Test performance_check in metrics", "taskName": null, "event": "performance_check", "component": "metrics", "status": "success", "duration_ms": 952, "user_id": "test_user_50", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.032589, "level": "ERROR", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 51, "message": "Test error in metrics", "taskName": null, "event": "shutdown", "component": "metrics", "status": "error", "error_type": "TestError", "error_code": 5236, "retry_count": 2, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.5337758, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 83, "message": "Application startup initiated", "taskName": null, "event": "application_startup", "component": "main", "status": "in_progress", "version": "1.0.0", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.5337758, "level": "INFO", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 90, "message": "Database connection established", "taskName": null, "event": "database_connection", "component": "database", "status": "success", "connection_pool_size": 10, "connection_timeout": 30, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.5337758, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 98, "message": "Voice agent initialized", "taskName": null, "event": "voice_agent_init", "component": "voice_agent", "status": "success", "model": "gpt-4", "voice_provider": "elevenlabs", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.5337758, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_5744", "phone_number": "+13579222526", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.537557, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_5744", "phone_number": "+13579222526", "call_duration": 218, "appointment_created": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.537557, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_7047", "phone_number": "+11702486879", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011987.5387695, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_7047", "phone_number": "+11702486879", "call_duration": 91, "appointment_created": true, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011987.5387695, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_9096", "phone_number": "+19975893704", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011988.540046, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_9096", "phone_number": "+19975893704", "call_duration": 202, "appointment_created": true, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011988.540046, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_3846", "phone_number": "+14308460123", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011989.5412755, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_3846", "phone_number": "+14308460123", "call_duration": 279, "appointment_created": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755011989.5412755, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 111, "message": "Customer call started", "taskName": null, "event": "call_started", "component": "voice_agent", "status": "success", "user_id": "user_9264", "phone_number": "+13768932391", "call_duration": 0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.5423574, "level": "INFO", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 122, "message": "Customer call ended", "taskName": null, "event": "call_ended", "component": "voice_agent", "status": "success", "user_id": "user_9264", "phone_number": "+13768932391", "call_duration": 293, "appointment_created": true, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.5423574, "level": "ERROR", "logger": "salon_ai.test_database", "module": "test_logging", "function": "test_logging", "line": 133, "message": "Database connection lost", "taskName": null, "event": "database_error", "component": "database", "status": "error", "error_type": "ConnectionError", "error_message": "Connection timeout after 30 seconds", "retry_attempt": 1, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.5423574, "level": "ERROR", "logger": "salon_ai.test_voice_agent", "module": "test_logging", "function": "test_logging", "line": 142, "message": "Voice synthesis failed", "taskName": null, "event": "voice_synthesis_error", "component": "voice_agent", "status": "error", "error_type": "APIError", "provider": "elevenlabs", "error_code": 429, "rate_limit_exceeded": true, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.5423574, "level": "INFO", "logger": "salon_ai.test_metrics", "module": "test_logging", "function": "test_logging", "line": 153, "message": "Performance metrics collected", "taskName": null, "event": "metrics_collection", "component": "metrics", "status": "success", "cpu_usage": 61, "memory_usage": 71, "active_calls": 2, "response_time_ms": 118, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.5423574, "level": "INFO", "logger": "salon_ai.test_main", "module": "test_logging", "function": "test_logging", "line": 163, "message": "Test logging completed", "taskName": null, "event": "test_completed", "component": "test", "status": "success", "total_logs_generated": 30, "test_duration": 30, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.8850734, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "drain", "line": 502, "message": "draining worker", "taskName": "Task-143", "id": "AW_2yvBMQ3rMzEF", "timeout": 1800, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": **********.901625, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "aclose", "line": 585, "message": "shutting down worker", "taskName": "Task-144", "id": "AW_2yvBMQ3rMzEF", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012506.8811305, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "starting worker", "taskName": "agent_runner", "version": "1.0.23", "rtc-version": "1.0.8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012506.8826497, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 403, "message": "preloading plugins", "taskName": "agent_runner", "packages": ["livekit.plugins.elevenlabs", "livekit.plugins.openai", "livekit.plugins.silero", "livekit.plugins.deepgram", "livekit.plugins.google"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012509.001554, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-3", "pid": 57, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012509.007603, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-4", "pid": 59, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012509.011262, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-5", "pid": 61, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012509.017709, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-6", "pid": 63, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012509.0243924, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-7", "pid": 65, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012509.0313582, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-8", "pid": 67, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012509.0401628, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-9", "pid": 69, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012509.046629, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-10", "pid": 71, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012509.0543647, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-11", "pid": 73, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012509.0719957, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-13", "pid": 75, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012509.0748048, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-14", "pid": 77, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012509.0849612, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-12", "pid": 79, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.3097677, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.3117127, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.3391411, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.3083074, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.345061, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-7", "pid": 65, "elapsed_time": 2.32, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.3763707, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-4", "pid": 59, "elapsed_time": 2.37, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.3774242, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-5", "pid": 61, "elapsed_time": 2.36, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.3786576, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-3", "pid": 57, "elapsed_time": 2.38, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.4845157, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-8", "pid": 67, "elapsed_time": 2.45, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.485841, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.4860895, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.5241146, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-14", "pid": 77, "elapsed_time": 2.45, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.5497503, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-9", "pid": 69, "elapsed_time": 2.51, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.550044, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.6005406, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-11", "pid": 73, "elapsed_time": 2.54, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.6019113, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-12", "pid": 79, "elapsed_time": 2.52, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.600758, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.6012552, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.6036203, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-6", "pid": 63, "elapsed_time": 2.58, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.600571, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.6055973, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-13", "pid": 75, "elapsed_time": 2.53, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.6062021, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.6505828, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-10", "pid": 71, "elapsed_time": 2.6, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.651124, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012511.6568353, "level": "INFO", "logger": "livekit.agents", "module": "_run", "function": "_worker_started", "line": 69, "message": "see tracing information at http://localhost:8081/debug", "taskName": "agent_runner", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012512.7891183, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 805, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_rcmLAMJ3vgr4", "url": "wss://salon-ai-p43dr7ww.livekit.cloud", "region": "US West B", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012523.9501805, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "drain", "line": 502, "message": "draining worker", "taskName": "Task-143", "id": "AW_rcmLAMJ3vgr4", "timeout": 1800, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012523.9577618, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "aclose", "line": 585, "message": "shutting down worker", "taskName": "Task-144", "id": "AW_rcmLAMJ3vgr4", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012640.3795474, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "starting worker", "taskName": "agent_runner", "version": "1.0.23", "rtc-version": "1.0.8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012640.3805265, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 403, "message": "preloading plugins", "taskName": "agent_runner", "packages": ["livekit.plugins.elevenlabs", "livekit.plugins.openai", "livekit.plugins.silero", "livekit.plugins.deepgram", "livekit.plugins.google"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012642.4095612, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-3", "pid": 57, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012642.4112935, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-4", "pid": 59, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012642.4134548, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-5", "pid": 61, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012642.4155195, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-7", "pid": 63, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012642.418358, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-6", "pid": 65, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012642.4205225, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-8", "pid": 67, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012642.4223197, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-9", "pid": 69, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012642.424853, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-10", "pid": 71, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012642.4271479, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-11", "pid": 73, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012642.4321442, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-12", "pid": 75, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012642.435749, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-13", "pid": 77, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012642.4404876, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-14", "pid": 79, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.6414442, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.6417398, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.6430566, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.6424067, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-7", "pid": 63, "elapsed_time": 2.23, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.6446784, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.6475246, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-9", "pid": 69, "elapsed_time": 2.22, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.6531193, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-8", "pid": 67, "elapsed_time": 2.23, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.6545296, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-10", "pid": 71, "elapsed_time": 2.23, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.66751, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-5", "pid": 61, "elapsed_time": 2.25, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.6671205, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.6839964, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-3", "pid": 57, "elapsed_time": 2.27, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.6840205, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.7000666, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-6", "pid": 65, "elapsed_time": 2.28, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.6999755, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.7248707, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-4", "pid": 59, "elapsed_time": 2.31, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.7249427, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.7651904, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-11", "pid": 73, "elapsed_time": 2.34, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.7652502, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.765582, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.7653384, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.7654147, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.7682457, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-14", "pid": 79, "elapsed_time": 2.33, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.7696047, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-12", "pid": 75, "elapsed_time": 2.34, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.77046, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-13", "pid": 77, "elapsed_time": 2.33, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012644.7724602, "level": "INFO", "logger": "livekit.agents", "module": "_run", "function": "_worker_started", "line": 69, "message": "see tracing information at http://localhost:8081/debug", "taskName": "agent_runner", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755012645.966038, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 805, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_SoybGcSibd8U", "url": "wss://salon-ai-p43dr7ww.livekit.cloud", "region": "US West B", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013536.3564556, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "drain", "line": 502, "message": "draining worker", "taskName": "Task-143", "id": "AW_SoybGcSibd8U", "timeout": 1800, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013536.3684561, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "aclose", "line": 585, "message": "shutting down worker", "taskName": "Task-144", "id": "AW_SoybGcSibd8U", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013557.3717754, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "starting worker", "taskName": "agent_runner", "version": "1.0.23", "rtc-version": "1.0.8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013557.3731635, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 403, "message": "preloading plugins", "taskName": "agent_runner", "packages": ["livekit.plugins.elevenlabs", "livekit.plugins.openai", "livekit.plugins.silero", "livekit.plugins.deepgram", "livekit.plugins.google"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013559.7347107, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-3", "pid": 58, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013559.7425332, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-4", "pid": 60, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013559.7501404, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-5", "pid": 62, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013559.7693994, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-6", "pid": 64, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013559.770782, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-8", "pid": 65, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013559.774108, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-7", "pid": 68, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013559.7782776, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-9", "pid": 70, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013559.783964, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-10", "pid": 72, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013559.788019, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-11", "pid": 74, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013559.792779, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-12", "pid": 76, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013559.8176985, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-13", "pid": 78, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013559.818801, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-14", "pid": 79, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.598764, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.6022542, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-3", "pid": 58, "elapsed_time": 2.87, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.615199, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.6226244, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.6380093, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-8", "pid": 65, "elapsed_time": 2.86, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.6575804, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-12", "pid": 76, "elapsed_time": 2.86, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.660848, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.6634488, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-5", "pid": 62, "elapsed_time": 2.91, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.6694782, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.670663, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-7", "pid": 68, "elapsed_time": 2.89, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.6721876, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.6752162, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-4", "pid": 60, "elapsed_time": 2.93, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.714114, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-10", "pid": 72, "elapsed_time": 2.93, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.7142751, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.73041, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-14", "pid": 79, "elapsed_time": 2.91, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.7308483, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.7325559, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-9", "pid": 70, "elapsed_time": 2.95, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.732678, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.7589421, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-11", "pid": 74, "elapsed_time": 2.97, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.7590518, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.776447, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-6", "pid": 64, "elapsed_time": 3.01, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.776528, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.8860567, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-13", "pid": 78, "elapsed_time": 3.07, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.8865297, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013562.8946936, "level": "INFO", "logger": "livekit.agents", "module": "_run", "function": "_worker_started", "line": 69, "message": "see tracing information at http://localhost:8081/debug", "taskName": "agent_runner", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755013563.2591195, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 805, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_cXCWKAyWQKJD", "url": "wss://salon-ai-p43dr7ww.livekit.cloud", "region": "India", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015885.391704, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "drain", "line": 502, "message": "draining worker", "taskName": "Task-142", "id": "AW_cXCWKAyWQKJD", "timeout": 1800, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015885.4226727, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "aclose", "line": 585, "message": "shutting down worker", "taskName": "Task-143", "id": "AW_cXCWKAyWQKJD", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015886.3673573, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Future exception was never retrieved\nfuture: <Future finished exception=ClientConnectionError('Connection lost: [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2706)')>", "exc_info": "Traceback (most recent call last):\n  File \"/usr/local/lib/python3.12/asyncio/sslproto.py\", line 651, in _do_shutdown\n    self._sslobj.unwrap()\n  File \"/usr/local/lib/python3.12/ssl.py\", line 920, in unwrap\n    return self._sslobj.shutdown()\n           ^^^^^^^^^^^^^^^^^^^^^^^\nssl.SSLError: [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2706)\n\nThe above exception was the direct cause of the following exception:\n\naiohttp.client_exceptions.ClientConnectionError: Connection lost: [SSL: APPLICATION_DATA_AFTER_CLOSE_NOTIFY] application data after close notify (_ssl.c:2706)", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015901.3312182, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "starting worker", "taskName": "agent_runner", "version": "1.0.23", "rtc-version": "1.0.8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015901.3323727, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 403, "message": "preloading plugins", "taskName": "agent_runner", "packages": ["livekit.plugins.elevenlabs", "livekit.plugins.openai", "livekit.plugins.silero", "livekit.plugins.deepgram", "livekit.plugins.google"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015903.6914678, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-3", "pid": 58, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015903.6984174, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-4", "pid": 60, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015903.703993, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-5", "pid": 62, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015903.7139695, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-6", "pid": 64, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015903.7216494, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-7", "pid": 66, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015903.7280462, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-8", "pid": 68, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015903.7325528, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-9", "pid": 70, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015903.7404327, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-11", "pid": 72, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015903.7524219, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-10", "pid": 74, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015903.7623208, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-12", "pid": 76, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015903.7684, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-13", "pid": 78, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015903.7883902, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-14", "pid": 80, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015907.6174984, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-4", "pid": 60, "elapsed_time": 3.92, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015907.6148455, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015907.8020327, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-13", "pid": 78, "elapsed_time": 4.03, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015907.8029513, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015907.8763657, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-6", "pid": 64, "elapsed_time": 4.16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015907.8786561, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015907.960703, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-12", "pid": 76, "elapsed_time": 4.2, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015907.9631238, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-5", "pid": 62, "elapsed_time": 4.26, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015907.9617023, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015907.971358, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-10", "pid": 74, "elapsed_time": 4.22, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015907.9664145, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015907.97279, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.064779, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-9", "pid": 70, "elapsed_time": 4.33, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.0664816, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-3", "pid": 58, "elapsed_time": 4.37, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.0654085, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.065993, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.1038005, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-8", "pid": 68, "elapsed_time": 4.37, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.1061466, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-7", "pid": 66, "elapsed_time": 4.38, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.1046748, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.1069443, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.2292182, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-14", "pid": 80, "elapsed_time": 4.44, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.229682, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.4543839, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-11", "pid": 72, "elapsed_time": 4.71, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.455088, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015908.4647696, "level": "INFO", "logger": "livekit.agents", "module": "_run", "function": "_worker_started", "line": 69, "message": "see tracing information at http://localhost:8081/debug", "taskName": "agent_runner", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015909.7995815, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 805, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_RL8BMBDjUjdp", "url": "wss://salon-dev-z1d1tpn4.livekit.cloud", "region": "US West B", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015934.4264512, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 876, "message": "received job request", "taskName": "Task-143", "job_id": "AJ_dxm4BtwG62R2", "dispatch_id": "", "room_name": "playground-URrc-PLuT", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015934.8690968, "level": "DEBUG", "logger": "tortoise", "module": "__init__", "function": "init", "line": 505, "message": "Tortoise-ORM startup\n    connections: {'default': {'engine': 'tortoise.backends.asyncpg', 'credentials': {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'password': 'uhmpBp***', 'database': 'postgres', 'minsize': 1, 'maxsize': 10, 'command_timeout': 60}}}\n    apps: {'models': {'models': ['models.customer', 'models.livekit', 'models.staff', 'models.service', 'models.appointment', 'models.recording', 'aerich.models'], 'default_connection': 'default'}}", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015934.9210029, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-145", "pid": 255, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015935.0165818, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015935.0178354, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "taskName": "job_user_entrypoint", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015935.0165818, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "taskName": "job_user_entrypoint", "pid": 60, "job_id": "AJ_dxm4BtwG62R2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015935.0178354, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "taskName": "job_user_entrypoint", "event": "application_startup", "component": "database", "status": "success", "pid": 60, "job_id": "AJ_dxm4BtwG62R2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015935.0540082, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_api::signal_client::signal_stream:106:livekit_api::signal_client::signal_stream - connecting to wss://salon-dev-z1d1tpn4.livekit.cloud/rtc?sdk=python&protocol=15&auto_subscribe=1&adaptive_stream=0&version=1.0.8&access_token=...", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015936.0456128, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-145", "pid": 255, "elapsed_time": 1.12, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015936.0455906, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015936.2431207, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::anchors:150:rustls::anchors - add_parsable_certificates processed 142 valid and 0 invalid certs", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015936.244807, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tokio_tungstenite::tls::encryption::rustls:103:tokio_tungstenite::tls::encryption::rustls - Added 142/142 native root certificates (ignored 0)", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015936.2456539, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:73:rustls::client::hs - No cached session for DnsName(\"salon-dev-z1d1tpn4.livekit.cloud\")", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015936.2464495, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:132:rustls::client::hs - Not resuming any session", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015938.4819875, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:615:rustls::client::hs - Using ciphersuite TLS13_AES_128_GCM_SHA256", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015938.4828925, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:142:rustls::client::tls13 - Not resuming", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015938.4840121, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:381:rustls::client::tls13 - TLS1.3 encrypted extensions: []", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015938.484969, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:472:rustls::client::hs - ALPN protocol is None", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015938.9892159, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::handshake::client:95:tungstenite::handshake::client - Client handshake done.", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015945.031596, "level": "WARNING", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "_warn_not_connected_task", "line": 268, "message": "The room connection was not established within 10 seconds after calling job_entry. This may indicate that job_ctx.connect() was not called. ", "taskName": "Task-7", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015945.031596, "level": "WARNING", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "_warn_not_connected_task", "line": 268, "message": "The room connection was not established within 10 seconds after calling job_entry. This may indicate that job_ctx.connect() was not called. ", "taskName": "Task-7", "pid": 60, "job_id": "AJ_dxm4BtwG62R2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015947.6198, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x7f80981cfa10>", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015947.6198, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x7f80981cfa10>", "taskName": "job_user_entrypoint", "pid": 60, "job_id": "AJ_dxm4BtwG62R2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015947.621448, "level": "WARNING", "logger": "livekit.agents", "module": "debug", "function": "instrumented", "line": 19, "message": "Running <Task finished name='job_user_entrypoint' coro=<entrypoint() done, defined at /app/main.py:85> result=None> took too long: 2.08 seconds", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015947.621448, "level": "WARNING", "logger": "livekit.agents", "module": "debug", "function": "instrumented", "line": 19, "message": "Running <Task finished name='job_user_entrypoint' coro=<entrypoint() done, defined at /app/main.py:85> result=None> took too long: 2.08 seconds", "taskName": null, "pid": 60, "job_id": "AJ_dxm4BtwG62R2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015947.6265795, "level": "DEBUG", "logger": "livekit.plugins.google", "module": "realtime_api", "function": "_main_task", "line": 479, "message": "connecting to Gemini Realtime API...", "taskName": "gemini-realtime-session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015948.876485, "level": "ERROR", "logger": "livekit", "module": "event_emitter", "function": "emit", "line": 62, "message": "failed to emit event track_subscribed", "exc_info": "Traceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/event_emitter.py\", line 58, in emit\n    callback(*callback_args)\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 175, in _on_track_available\n    self._stream = self._create_stream(track)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 222, in _create_stream\n    return rtc.AudioStream.from_track(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 206, in from_track\n    return AudioStream(\n           ^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 118, in __init__\n    stream = self._create_owned_stream()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 230, in _create_owned_stream\n    resp = FfiClient.instance.request(req)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_ffi_client.py\", line 239, in request\n    assert handle != INVALID_HANDLE\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError", "taskName": "Task-8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015948.8771079, "level": "ERROR", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_ffi::cabi:62:livekit_ffi::cabi - failed to handle request FfiRequest { message: Some(NewAudioStream(NewAudioStreamRequest { track_handle: 9, r#type: AudioStreamNative, sample_rate: Some(24000), num_channels: Some(1), audio_filter_module_id: Some(\"140190354468880\"), audio_filter_options: Some(\"{\\\"modelPath\\\": \\\"/usr/local/lib/python3.12/site-packages/livekit/plugins/noise_cancellation/resources/inb.bvc.hs.c6.w.s.23cdb3.kef\\\"}\") })) }: InvalidRequest(\"handle is not a livekit_ffi::server::room::FfiTrack\")", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015948.876485, "level": "ERROR", "logger": "livekit", "module": "event_emitter", "function": "emit", "line": 62, "message": "failed to emit event track_subscribed\nTraceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/event_emitter.py\", line 58, in emit\n    callback(*callback_args)\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 175, in _on_track_available\n    self._stream = self._create_stream(track)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 222, in _create_stream\n    return rtc.AudioStream.from_track(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 206, in from_track\n    return AudioStream(\n           ^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 118, in __init__\n    stream = self._create_owned_stream()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 230, in _create_owned_stream\n    resp = FfiClient.instance.request(req)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_ffi_client.py\", line 239, in request\n    assert handle != INVALID_HANDLE\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError", "taskName": "Task-8", "pid": 60, "job_id": "AJ_dxm4BtwG62R2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015948.8771079, "level": "ERROR", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_ffi::cabi:62:livekit_ffi::cabi - failed to handle request FfiRequest { message: Some(NewAudioStream(NewAudioStreamRequest { track_handle: 9, r#type: AudioStreamNative, sample_rate: Some(24000), num_channels: Some(1), audio_filter_module_id: Some(\"140190354468880\"), audio_filter_options: Some(\"{\\\"modelPath\\\": \\\"/usr/local/lib/python3.12/site-packages/livekit/plugins/noise_cancellation/resources/inb.bvc.hs.c6.w.s.23cdb3.kef\\\"}\") })) }: InvalidRequest(\"handle is not a livekit_ffi::server::room::FfiTrack\")", "taskName": null, "pid": 60, "job_id": "AJ_dxm4BtwG62R2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015948.8912117, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CONNECTING", "taskName": "gemini-realtime-session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015948.9998653, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 310, "message": "> GET //ws/google.ai.generativelanguage.v1beta.GenerativeService.BidiGenerateContent?key=AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M HTTP/1.1", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981cf9e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.0009785, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Host: generativelanguage.googleapis.com", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981cf9e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.0020888, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Upgrade: websocket", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981cf9e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.0031059, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Connection: Upgrade", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981cf9e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.0044577, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Key: AO7Fl/wXoH4FNjd+gNa4rQ==", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981cf9e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.0056343, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Version: 13", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981cf9e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.0065076, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981cf9e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.0076716, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Content-Type: application/json", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981cf9e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.008972, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-key: AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981cf9e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.0098474, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> user-agent: google-genai-sdk/1.17.0 gl-python/3.12.11", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981cf9e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.0113428, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-client: google-genai-sdk/1.17.0 gl-python/3.12.11", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981cf9e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.0125873, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> User-Agent: Python/3.12 websockets/13.1", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981cf9e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.1116903, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::protocol:666:tungstenite::protocol - Received close frame: Some(CloseFrame { code: Normal, reason: \"\" })", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.1187341, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit::room:1257:livekit::room - disconnected from room with reason: ClientInitiated", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.1194055, "level": "ERROR", "logger": "livekit", "module": "_utils", "function": "task_done_logger", "line": 39, "message": "task exception: <Task finished name='Task-25' coro=<AudioStream._run() done, defined at /usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py:252> exception=AttributeError(\"'AudioStream' object has no attribute '_ffi_handle'\")>", "exc_info": "Traceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 254, in _run\n    event = await self._ffi_queue.wait_for(self._is_event)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_utils.py\", line 82, in wait_for\n    if fnc(event):\n       ^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 278, in _is_event\n    return e.audio_stream_event.stream_handle == self._ffi_handle.handle\n                                                 ^^^^^^^^^^^^^^^^\nAttributeError: 'AudioStream' object has no attribute '_ffi_handle'", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.1194055, "level": "ERROR", "logger": "livekit", "module": "_utils", "function": "task_done_logger", "line": 39, "message": "task exception: <Task finished name='Task-25' coro=<AudioStream._run() done, defined at /usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py:252> exception=AttributeError(\"'AudioStream' object has no attribute '_ffi_handle'\")>\nTraceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 254, in _run\n    event = await self._ffi_queue.wait_for(self._is_event)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_utils.py\", line 82, in wait_for\n    if fnc(event):\n       ^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 278, in _is_event\n    return e.audio_stream_event.stream_handle == self._ffi_handle.handle\n                                                 ^^^^^^^^^^^^^^^^\nAttributeError: 'AudioStream' object has no attribute '_ffi_handle'", "taskName": null, "pid": 60, "job_id": "AJ_dxm4BtwG62R2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.1250057, "level": "DEBUG", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "_run_job_task", "line": 294, "message": "shutting down job task", "taskName": "job_task", "reason": "room disconnected", "user_initiated": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.126312, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "_read_ipc_task", "line": 315, "message": "process exiting", "taskName": "Task-53", "reason": "room disconnected", "pid": 60, "job_id": "AJ_dxm4BtwG62R2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.1271853, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.1284566, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.1271853, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "taskName": "job_shutdown_callback", "pid": 60, "job_id": "AJ_dxm4BtwG62R2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.1313872, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "taskName": "job_shutdown_callback", "event": "application_shutdown", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.1284566, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "taskName": "job_shutdown_callback", "pid": 60, "job_id": "AJ_dxm4BtwG62R2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.133201, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_close_http_ctx", "line": 53, "message": "http_session(): closing the httpclient ctx", "taskName": "job_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.1313872, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "taskName": "job_shutdown_callback", "event": "application_shutdown", "component": "database", "status": "success", "pid": 60, "job_id": "AJ_dxm4BtwG62R2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015949.134321, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_new_session", "line": 20, "message": "http_session(): creating a new httpclient ctx", "taskName": "job_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015988.263121, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 876, "message": "received job request", "taskName": "Task-157", "job_id": "AJ_MUjhMqGGiDGS", "dispatch_id": "", "room_name": "playground-8ev8-M8VI", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015988.5411859, "level": "DEBUG", "logger": "tortoise", "module": "__init__", "function": "init", "line": 505, "message": "Tortoise-ORM startup\n    connections: {'default': {'engine': 'tortoise.backends.asyncpg', 'credentials': {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'password': 'uhmpBp***', 'database': 'postgres', 'minsize': 1, 'maxsize': 10, 'command_timeout': 60}}}\n    apps: {'models': {'models': ['models.customer', 'models.livekit', 'models.staff', 'models.service', 'models.appointment', 'models.recording', 'aerich.models'], 'default_connection': 'default'}}", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015988.6125557, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-159", "pid": 306, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015988.6421192, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015988.643111, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "taskName": "job_user_entrypoint", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015988.6421192, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "taskName": "job_user_entrypoint", "pid": 78, "job_id": "AJ_MUjhMqGGiDGS", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015988.643111, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "taskName": "job_user_entrypoint", "event": "application_startup", "component": "database", "status": "success", "pid": 78, "job_id": "AJ_MUjhMqGGiDGS", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015988.6550512, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_api::signal_client::signal_stream:106:livekit_api::signal_client::signal_stream - connecting to wss://salon-dev-z1d1tpn4.livekit.cloud/rtc?sdk=python&protocol=15&auto_subscribe=1&adaptive_stream=0&version=1.0.8&access_token=...", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015988.9652543, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::anchors:150:rustls::anchors - add_parsable_certificates processed 142 valid and 0 invalid certs", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015988.966393, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tokio_tungstenite::tls::encryption::rustls:103:tokio_tungstenite::tls::encryption::rustls - Added 142/142 native root certificates (ignored 0)", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015988.9672954, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:73:rustls::client::hs - No cached session for DnsName(\"salon-dev-z1d1tpn4.livekit.cloud\")", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015988.9683163, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:132:rustls::client::hs - Not resuming any session", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015989.2435243, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:615:rustls::client::hs - Using ciphersuite TLS13_AES_128_GCM_SHA256", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015989.2448027, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:142:rustls::client::tls13 - Not resuming", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015989.2464113, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:381:rustls::client::tls13 - TLS1.3 encrypted extensions: []", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015989.247676, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:472:rustls::client::hs - ALPN protocol is None", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015989.5353107, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::handshake::client:95:tungstenite::handshake::client - Client handshake done.", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015989.6150076, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-159", "pid": 306, "elapsed_time": 1.0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015989.615032, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015995.5666127, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x7f80981d3980>", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015995.5666127, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x7f80981d3980>", "taskName": "job_user_entrypoint", "pid": 78, "job_id": "AJ_MUjhMqGGiDGS", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015995.5732546, "level": "DEBUG", "logger": "livekit.plugins.google", "module": "realtime_api", "function": "_main_task", "line": 479, "message": "connecting to Gemini Realtime API...", "taskName": "gemini-realtime-session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.8669827, "level": "ERROR", "logger": "livekit", "module": "event_emitter", "function": "emit", "line": 62, "message": "failed to emit event track_subscribed", "exc_info": "Traceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/event_emitter.py\", line 58, in emit\n    callback(*callback_args)\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 175, in _on_track_available\n    self._stream = self._create_stream(track)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 222, in _create_stream\n    return rtc.AudioStream.from_track(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 206, in from_track\n    return AudioStream(\n           ^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 118, in __init__\n    stream = self._create_owned_stream()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 230, in _create_owned_stream\n    resp = FfiClient.instance.request(req)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_ffi_client.py\", line 239, in request\n    assert handle != INVALID_HANDLE\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError", "taskName": "Task-8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.8675778, "level": "ERROR", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_ffi::cabi:62:livekit_ffi::cabi - failed to handle request FfiRequest { message: Some(NewAudioStream(NewAudioStreamRequest { track_handle: 9, r#type: AudioStreamNative, sample_rate: Some(24000), num_channels: Some(1), audio_filter_module_id: Some(\"140190354468880\"), audio_filter_options: Some(\"{\\\"modelPath\\\": \\\"/usr/local/lib/python3.12/site-packages/livekit/plugins/noise_cancellation/resources/inb.bvc.hs.c6.w.s.23cdb3.kef\\\"}\") })) }: InvalidRequest(\"handle is not a livekit_ffi::server::room::FfiTrack\")", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.8669827, "level": "ERROR", "logger": "livekit", "module": "event_emitter", "function": "emit", "line": 62, "message": "failed to emit event track_subscribed\nTraceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/event_emitter.py\", line 58, in emit\n    callback(*callback_args)\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 175, in _on_track_available\n    self._stream = self._create_stream(track)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 222, in _create_stream\n    return rtc.AudioStream.from_track(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 206, in from_track\n    return AudioStream(\n           ^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 118, in __init__\n    stream = self._create_owned_stream()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 230, in _create_owned_stream\n    resp = FfiClient.instance.request(req)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_ffi_client.py\", line 239, in request\n    assert handle != INVALID_HANDLE\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError", "taskName": "Task-8", "pid": 78, "job_id": "AJ_MUjhMqGGiDGS", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.8675778, "level": "ERROR", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_ffi::cabi:62:livekit_ffi::cabi - failed to handle request FfiRequest { message: Some(NewAudioStream(NewAudioStreamRequest { track_handle: 9, r#type: AudioStreamNative, sample_rate: Some(24000), num_channels: Some(1), audio_filter_module_id: Some(\"140190354468880\"), audio_filter_options: Some(\"{\\\"modelPath\\\": \\\"/usr/local/lib/python3.12/site-packages/livekit/plugins/noise_cancellation/resources/inb.bvc.hs.c6.w.s.23cdb3.kef\\\"}\") })) }: InvalidRequest(\"handle is not a livekit_ffi::server::room::FfiTrack\")", "taskName": null, "pid": 78, "job_id": "AJ_MUjhMqGGiDGS", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.8821728, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CONNECTING", "taskName": "gemini-realtime-session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.9467018, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 310, "message": "> GET //ws/google.ai.generativelanguage.v1beta.GenerativeService.BidiGenerateContent?key=AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M HTTP/1.1", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370f8c0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.947899, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Host: generativelanguage.googleapis.com", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370f8c0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.9493628, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Upgrade: websocket", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370f8c0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.9507804, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Connection: Upgrade", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370f8c0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.952073, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Key: 4OlwNRDdXwcOr8Uy/LE0bQ==", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370f8c0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.9533377, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Version: 13", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370f8c0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.9541795, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370f8c0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.9550054, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Content-Type: application/json", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370f8c0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.9561398, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-key: AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370f8c0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.95734, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> user-agent: google-genai-sdk/1.17.0 gl-python/3.12.11", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370f8c0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.9585032, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-client: google-genai-sdk/1.17.0 gl-python/3.12.11", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370f8c0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015996.9593697, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> User-Agent: Python/3.12 websockets/13.1", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370f8c0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.1122253, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::protocol:666:tungstenite::protocol - Received close frame: None", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.1132493, "level": "DEBUG", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "_run_job_task", "line": 294, "message": "shutting down job task", "taskName": "job_task", "reason": "", "user_initiated": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.1154132, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "_read_ipc_task", "line": 315, "message": "process exiting", "taskName": "Task-60", "reason": "", "pid": 78, "job_id": "AJ_MUjhMqGGiDGS", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.1190274, "level": "ERROR", "logger": "livekit", "module": "_utils", "function": "task_done_logger", "line": 39, "message": "task exception: <Task finished name='Task-25' coro=<AudioStream._run() done, defined at /usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py:252> exception=AttributeError(\"'AudioStream' object has no attribute '_ffi_handle'\")>", "exc_info": "Traceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 254, in _run\n    event = await self._ffi_queue.wait_for(self._is_event)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_utils.py\", line 82, in wait_for\n    if fnc(event):\n       ^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 278, in _is_event\n    return e.audio_stream_event.stream_handle == self._ffi_handle.handle\n                                                 ^^^^^^^^^^^^^^^^\nAttributeError: 'AudioStream' object has no attribute '_ffi_handle'", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.121434, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit::room:1257:livekit::room - disconnected from room with reason: ClientInitiated", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.1190274, "level": "ERROR", "logger": "livekit", "module": "_utils", "function": "task_done_logger", "line": 39, "message": "task exception: <Task finished name='Task-25' coro=<AudioStream._run() done, defined at /usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py:252> exception=AttributeError(\"'AudioStream' object has no attribute '_ffi_handle'\")>\nTraceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 254, in _run\n    event = await self._ffi_queue.wait_for(self._is_event)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_utils.py\", line 82, in wait_for\n    if fnc(event):\n       ^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 278, in _is_event\n    return e.audio_stream_event.stream_handle == self._ffi_handle.handle\n                                                 ^^^^^^^^^^^^^^^^\nAttributeError: 'AudioStream' object has no attribute '_ffi_handle'", "taskName": null, "pid": 78, "job_id": "AJ_MUjhMqGGiDGS", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.126657, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.1277988, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.126657, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "taskName": "job_shutdown_callback", "pid": 78, "job_id": "AJ_MUjhMqGGiDGS", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.1277988, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "taskName": "job_shutdown_callback", "pid": 78, "job_id": "AJ_MUjhMqGGiDGS", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.129164, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "taskName": "job_shutdown_callback", "event": "application_shutdown", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.1324897, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_close_http_ctx", "line": 53, "message": "http_session(): closing the httpclient ctx", "taskName": "job_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.129164, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "taskName": "job_shutdown_callback", "event": "application_shutdown", "component": "database", "status": "success", "pid": 78, "job_id": "AJ_MUjhMqGGiDGS", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755015997.1341357, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_new_session", "line": 20, "message": "http_session(): creating a new httpclient ctx", "taskName": "job_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016052.5853322, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 876, "message": "received job request", "taskName": "Task-171", "job_id": "AJ_cyvchDE3kQBq", "dispatch_id": "", "room_name": "playground-11yj-w52v", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016052.837659, "level": "DEBUG", "logger": "tortoise", "module": "__init__", "function": "init", "line": 505, "message": "Tortoise-ORM startup\n    connections: {'default': {'engine': 'tortoise.backends.asyncpg', 'credentials': {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'password': 'uhmpBp***', 'database': 'postgres', 'minsize': 1, 'maxsize': 10, 'command_timeout': 60}}}\n    apps: {'models': {'models': ['models.customer', 'models.livekit', 'models.staff', 'models.service', 'models.appointment', 'models.recording', 'aerich.models'], 'default_connection': 'default'}}", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016052.8737397, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-173", "pid": 359, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016052.9316955, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016052.9332132, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "taskName": "job_user_entrypoint", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016052.9316955, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "taskName": "job_user_entrypoint", "pid": 64, "job_id": "AJ_cyvchDE3kQBq", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016052.9332132, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "taskName": "job_user_entrypoint", "event": "application_startup", "component": "database", "status": "success", "pid": 64, "job_id": "AJ_cyvchDE3kQBq", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016052.9459429, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_api::signal_client::signal_stream:106:livekit_api::signal_client::signal_stream - connecting to wss://salon-dev-z1d1tpn4.livekit.cloud/rtc?sdk=python&protocol=15&auto_subscribe=1&adaptive_stream=0&version=1.0.8&access_token=...", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016053.2771258, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::anchors:150:rustls::anchors - add_parsable_certificates processed 142 valid and 0 invalid certs", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016053.2784953, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tokio_tungstenite::tls::encryption::rustls:103:tokio_tungstenite::tls::encryption::rustls - Added 142/142 native root certificates (ignored 0)", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016053.2795544, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:73:rustls::client::hs - No cached session for DnsName(\"salon-dev-z1d1tpn4.livekit.cloud\")", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016053.2808444, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:132:rustls::client::hs - Not resuming any session", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016053.5594544, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:615:rustls::client::hs - Using ciphersuite TLS13_AES_128_GCM_SHA256", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016053.5604353, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:142:rustls::client::tls13 - Not resuming", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016053.5618553, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:381:rustls::client::tls13 - TLS1.3 encrypted extensions: []", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016053.562885, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:472:rustls::client::hs - ALPN protocol is None", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016053.8164387, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::handshake::client:95:tungstenite::handshake::client - Client handshake done.", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016053.866535, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016053.8666134, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-173", "pid": 359, "elapsed_time": 0.99, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016059.9200807, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x7f80981cfa70>", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016059.9200807, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x7f80981cfa70>", "taskName": "job_user_entrypoint", "pid": 64, "job_id": "AJ_cyvchDE3kQBq", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016059.926676, "level": "DEBUG", "logger": "livekit.plugins.google", "module": "realtime_api", "function": "_main_task", "line": 479, "message": "connecting to Gemini Realtime API...", "taskName": "gemini-realtime-session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.1782324, "level": "ERROR", "logger": "livekit", "module": "event_emitter", "function": "emit", "line": 62, "message": "failed to emit event track_subscribed", "exc_info": "Traceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/event_emitter.py\", line 58, in emit\n    callback(*callback_args)\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 175, in _on_track_available\n    self._stream = self._create_stream(track)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 222, in _create_stream\n    return rtc.AudioStream.from_track(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 206, in from_track\n    return AudioStream(\n           ^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 118, in __init__\n    stream = self._create_owned_stream()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 230, in _create_owned_stream\n    resp = FfiClient.instance.request(req)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_ffi_client.py\", line 239, in request\n    assert handle != INVALID_HANDLE\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError", "taskName": "Task-8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.1791322, "level": "ERROR", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_ffi::cabi:62:livekit_ffi::cabi - failed to handle request FfiRequest { message: Some(NewAudioStream(NewAudioStreamRequest { track_handle: 8, r#type: AudioStreamNative, sample_rate: Some(24000), num_channels: Some(1), audio_filter_module_id: Some(\"140190354468880\"), audio_filter_options: Some(\"{\\\"modelPath\\\": \\\"/usr/local/lib/python3.12/site-packages/livekit/plugins/noise_cancellation/resources/inb.bvc.hs.c6.w.s.23cdb3.kef\\\"}\") })) }: InvalidRequest(\"handle is not a livekit_ffi::server::room::FfiTrack\")", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.1782324, "level": "ERROR", "logger": "livekit", "module": "event_emitter", "function": "emit", "line": 62, "message": "failed to emit event track_subscribed\nTraceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/event_emitter.py\", line 58, in emit\n    callback(*callback_args)\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 175, in _on_track_available\n    self._stream = self._create_stream(track)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 222, in _create_stream\n    return rtc.AudioStream.from_track(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 206, in from_track\n    return AudioStream(\n           ^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 118, in __init__\n    stream = self._create_owned_stream()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 230, in _create_owned_stream\n    resp = FfiClient.instance.request(req)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_ffi_client.py\", line 239, in request\n    assert handle != INVALID_HANDLE\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError", "taskName": "Task-8", "pid": 64, "job_id": "AJ_cyvchDE3kQBq", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.1791322, "level": "ERROR", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_ffi::cabi:62:livekit_ffi::cabi - failed to handle request FfiRequest { message: Some(NewAudioStream(NewAudioStreamRequest { track_handle: 8, r#type: AudioStreamNative, sample_rate: Some(24000), num_channels: Some(1), audio_filter_module_id: Some(\"140190354468880\"), audio_filter_options: Some(\"{\\\"modelPath\\\": \\\"/usr/local/lib/python3.12/site-packages/livekit/plugins/noise_cancellation/resources/inb.bvc.hs.c6.w.s.23cdb3.kef\\\"}\") })) }: InvalidRequest(\"handle is not a livekit_ffi::server::room::FfiTrack\")", "taskName": null, "pid": 64, "job_id": "AJ_cyvchDE3kQBq", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.2006562, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CONNECTING", "taskName": "gemini-realtime-session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.3161592, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 310, "message": "> GET //ws/google.ai.generativelanguage.v1beta.GenerativeService.BidiGenerateContent?key=AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M HTTP/1.1", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370dfd0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.3174367, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Host: generativelanguage.googleapis.com", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370dfd0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.3188927, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Upgrade: websocket", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370dfd0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.3196874, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Connection: Upgrade", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370dfd0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.320443, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Key: iFJ/YA2vkjAjLuDuplRmTw==", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370dfd0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.3213294, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Version: 13", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370dfd0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.3222249, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370dfd0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.323131, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Content-Type: application/json", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370dfd0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.3239417, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-key: AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370dfd0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.324811, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> user-agent: google-genai-sdk/1.17.0 gl-python/3.12.11", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370dfd0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.3255334, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-client: google-genai-sdk/1.17.0 gl-python/3.12.11", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370dfd0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.3262753, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> User-Agent: Python/3.12 websockets/13.1", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f808370dfd0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.4332745, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::protocol:666:tungstenite::protocol - Received close frame: None", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.4400032, "level": "ERROR", "logger": "livekit", "module": "_utils", "function": "task_done_logger", "line": 39, "message": "task exception: <Task finished name='Task-25' coro=<AudioStream._run() done, defined at /usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py:252> exception=AttributeError(\"'AudioStream' object has no attribute '_ffi_handle'\")>", "exc_info": "Traceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 254, in _run\n    event = await self._ffi_queue.wait_for(self._is_event)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_utils.py\", line 82, in wait_for\n    if fnc(event):\n       ^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 278, in _is_event\n    return e.audio_stream_event.stream_handle == self._ffi_handle.handle\n                                                 ^^^^^^^^^^^^^^^^\nAttributeError: 'AudioStream' object has no attribute '_ffi_handle'", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.4438498, "level": "DEBUG", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "_run_job_task", "line": 294, "message": "shutting down job task", "taskName": "job_task", "reason": "", "user_initiated": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.4400032, "level": "ERROR", "logger": "livekit", "module": "_utils", "function": "task_done_logger", "line": 39, "message": "task exception: <Task finished name='Task-25' coro=<AudioStream._run() done, defined at /usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py:252> exception=AttributeError(\"'AudioStream' object has no attribute '_ffi_handle'\")>\nTraceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 254, in _run\n    event = await self._ffi_queue.wait_for(self._is_event)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_utils.py\", line 82, in wait_for\n    if fnc(event):\n       ^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 278, in _is_event\n    return e.audio_stream_event.stream_handle == self._ffi_handle.handle\n                                                 ^^^^^^^^^^^^^^^^\nAttributeError: 'AudioStream' object has no attribute '_ffi_handle'", "taskName": null, "pid": 64, "job_id": "AJ_cyvchDE3kQBq", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.4452195, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "_read_ipc_task", "line": 315, "message": "process exiting", "taskName": "Task-67", "reason": "", "pid": 64, "job_id": "AJ_cyvchDE3kQBq", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.4457562, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.4467154, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.4457562, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "taskName": "job_shutdown_callback", "pid": 64, "job_id": "AJ_cyvchDE3kQBq", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.447682, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "taskName": "job_shutdown_callback", "event": "application_shutdown", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.4467154, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "taskName": "job_shutdown_callback", "pid": 64, "job_id": "AJ_cyvchDE3kQBq", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.4488122, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_close_http_ctx", "line": 53, "message": "http_session(): closing the httpclient ctx", "taskName": "job_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.447682, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "taskName": "job_shutdown_callback", "event": "application_shutdown", "component": "database", "status": "success", "pid": 64, "job_id": "AJ_cyvchDE3kQBq", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016061.4497814, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_new_session", "line": 20, "message": "http_session(): creating a new httpclient ctx", "taskName": "job_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016278.2253144, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 389, "message": "starting worker", "taskName": "agent_runner", "version": "1.2.5", "rtc-version": "1.0.12", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016278.233887, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 151, "message": "initializing job runner", "taskName": "Task-5", "tid": 26012, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016278.234897, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 151, "message": "initializing job runner", "taskName": "Task-6", "tid": 25008, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016278.2358963, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 151, "message": "initializing job runner", "taskName": "Task-7", "tid": 43392, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016278.2358963, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 151, "message": "initializing job runner", "taskName": "Task-8", "tid": 40856, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016278.2368975, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 160, "message": "job runner initialized", "taskName": "Task-5", "tid": 26012, "elapsed_time": 0.0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016278.237894, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 160, "message": "job runner initialized", "taskName": "Task-6", "tid": 25008, "elapsed_time": 0.0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016278.239043, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 160, "message": "job runner initialized", "taskName": "Task-7", "tid": 43392, "elapsed_time": 0.0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016278.2409933, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 160, "message": "job runner initialized", "taskName": "Task-8", "tid": 40856, "elapsed_time": 0.01, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016279.4394581, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 799, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_ZL6jggYhTpFh", "url": "wss://salon-dev-z1d1tpn4.livekit.cloud", "region": "US West B", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016304.1842463, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 870, "message": "received job request", "taskName": "Task-53", "job_id": "AJ_YP8QeJr34UWr", "dispatch_id": "", "room_name": "playground-7sLN-lGgM", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016304.4762342, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 151, "message": "initializing job runner", "taskName": "Task-58", "tid": 41596, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016304.4762342, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 160, "message": "job runner initialized", "taskName": "Task-58", "tid": 41596, "elapsed_time": 0.0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016304.6129768, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016304.6129768, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "taskName": "job_user_entrypoint", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016312.89686, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x000001F7379C70B0>", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016313.155659, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016313.156656, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016313.1571722, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "taskName": "job_shutdown_callback", "event": "application_shutdown", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016338.4389656, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 876, "message": "received job request", "taskName": "Task-185", "job_id": "AJ_A8BJzed8moPR", "dispatch_id": "", "room_name": "playground-Nfqq-o6rp", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016338.7512856, "level": "DEBUG", "logger": "tortoise", "module": "__init__", "function": "init", "line": 505, "message": "Tortoise-ORM startup\n    connections: {'default': {'engine': 'tortoise.backends.asyncpg', 'credentials': {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'password': 'uhmpBp***', 'database': 'postgres', 'minsize': 1, 'maxsize': 10, 'command_timeout': 60}}}\n    apps: {'models': {'models': ['models.customer', 'models.livekit', 'models.staff', 'models.service', 'models.appointment', 'models.recording', 'aerich.models'], 'default_connection': 'default'}}", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016338.851267, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 166, "message": "initializing process", "taskName": "Task-187", "pid": 433, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.0680416, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.0693634, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "taskName": "job_user_entrypoint", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.0680416, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "taskName": "job_user_entrypoint", "pid": 76, "job_id": "AJ_A8BJzed8moPR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.0693634, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "taskName": "job_user_entrypoint", "event": "application_startup", "component": "database", "status": "success", "pid": 76, "job_id": "AJ_A8BJzed8moPR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.110479, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_api::signal_client::signal_stream:106:livekit_api::signal_client::signal_stream - connecting to wss://salon-dev-z1d1tpn4.livekit.cloud/rtc?sdk=python&protocol=15&auto_subscribe=1&adaptive_stream=0&version=1.0.8&access_token=...", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.4114804, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::anchors:150:rustls::anchors - add_parsable_certificates processed 142 valid and 0 invalid certs", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.4128094, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tokio_tungstenite::tls::encryption::rustls:103:tokio_tungstenite::tls::encryption::rustls - Added 142/142 native root certificates (ignored 0)", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.4136822, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:73:rustls::client::hs - No cached session for DnsName(\"salon-dev-z1d1tpn4.livekit.cloud\")", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.4147031, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:132:rustls::client::hs - Not resuming any session", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.669701, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:615:rustls::client::hs - Using ciphersuite TLS13_AES_128_GCM_SHA256", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.6714165, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:142:rustls::client::tls13 - Not resuming", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.6732702, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:381:rustls::client::tls13 - TLS1.3 encrypted extensions: []", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.676016, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:472:rustls::client::hs - ALPN protocol is None", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016339.913719, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::handshake::client:95:tungstenite::handshake::client - Client handshake done.", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016340.5082035, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016340.5083892, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 181, "message": "process initialized", "taskName": "Task-187", "pid": 433, "elapsed_time": 1.68, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016346.2088637, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x7f80981d38c0>", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016346.2088637, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x7f80981d38c0>", "taskName": "job_user_entrypoint", "pid": 76, "job_id": "AJ_A8BJzed8moPR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016346.2208672, "level": "DEBUG", "logger": "livekit.plugins.google", "module": "realtime_api", "function": "_main_task", "line": 479, "message": "connecting to Gemini Realtime API...", "taskName": "gemini-realtime-session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.4285288, "level": "ERROR", "logger": "livekit", "module": "event_emitter", "function": "emit", "line": 62, "message": "failed to emit event track_subscribed", "exc_info": "Traceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/event_emitter.py\", line 58, in emit\n    callback(*callback_args)\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 175, in _on_track_available\n    self._stream = self._create_stream(track)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 222, in _create_stream\n    return rtc.AudioStream.from_track(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 206, in from_track\n    return AudioStream(\n           ^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 118, in __init__\n    stream = self._create_owned_stream()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 230, in _create_owned_stream\n    resp = FfiClient.instance.request(req)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_ffi_client.py\", line 239, in request\n    assert handle != INVALID_HANDLE\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError", "taskName": "Task-8", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.4296272, "level": "ERROR", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_ffi::cabi:62:livekit_ffi::cabi - failed to handle request FfiRequest { message: Some(NewAudioStream(NewAudioStreamRequest { track_handle: 10, r#type: AudioStreamNative, sample_rate: Some(24000), num_channels: Some(1), audio_filter_module_id: Some(\"140190354468880\"), audio_filter_options: Some(\"{\\\"modelPath\\\": \\\"/usr/local/lib/python3.12/site-packages/livekit/plugins/noise_cancellation/resources/inb.bvc.hs.c6.w.s.23cdb3.kef\\\"}\") })) }: InvalidRequest(\"handle is not a livekit_ffi::server::room::FfiTrack\")", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.4285288, "level": "ERROR", "logger": "livekit", "module": "event_emitter", "function": "emit", "line": 62, "message": "failed to emit event track_subscribed\nTraceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/event_emitter.py\", line 58, in emit\n    callback(*callback_args)\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 175, in _on_track_available\n    self._stream = self._create_stream(track)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/agents/voice/room_io/_input.py\", line 222, in _create_stream\n    return rtc.AudioStream.from_track(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 206, in from_track\n    return AudioStream(\n           ^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 118, in __init__\n    stream = self._create_owned_stream()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 230, in _create_owned_stream\n    resp = FfiClient.instance.request(req)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_ffi_client.py\", line 239, in request\n    assert handle != INVALID_HANDLE\n           ^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError", "taskName": "Task-8", "pid": 76, "job_id": "AJ_A8BJzed8moPR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.4296272, "level": "ERROR", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_ffi::cabi:62:livekit_ffi::cabi - failed to handle request FfiRequest { message: Some(NewAudioStream(NewAudioStreamRequest { track_handle: 10, r#type: AudioStreamNative, sample_rate: Some(24000), num_channels: Some(1), audio_filter_module_id: Some(\"140190354468880\"), audio_filter_options: Some(\"{\\\"modelPath\\\": \\\"/usr/local/lib/python3.12/site-packages/livekit/plugins/noise_cancellation/resources/inb.bvc.hs.c6.w.s.23cdb3.kef\\\"}\") })) }: InvalidRequest(\"handle is not a livekit_ffi::server::room::FfiTrack\")", "taskName": null, "pid": 76, "job_id": "AJ_A8BJzed8moPR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.4480648, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CONNECTING", "taskName": "gemini-realtime-session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.5195, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 310, "message": "> GET //ws/google.ai.generativelanguage.v1beta.GenerativeService.BidiGenerateContent?key=AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M HTTP/1.1", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981d3410>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.5211241, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Host: generativelanguage.googleapis.com", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981d3410>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.5221856, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Upgrade: websocket", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981d3410>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.523052, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Connection: Upgrade", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981d3410>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.5240867, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Key: 1/E+T4Wg0Nx7D8o7oT7RCw==", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981d3410>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.5249228, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Version: 13", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981d3410>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.5257568, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981d3410>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.5275111, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Content-Type: application/json", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981d3410>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.5284781, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-key: AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981d3410>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.5294607, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> user-agent: google-genai-sdk/1.17.0 gl-python/3.12.11", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981d3410>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.5306365, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-client: google-genai-sdk/1.17.0 gl-python/3.12.11", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981d3410>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.5313911, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> User-Agent: Python/3.12 websockets/13.1", "taskName": "gemini-realtime-session", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f80981d3410>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.7049367, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::protocol:666:tungstenite::protocol - Received close frame: None", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.7128408, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit::room:1257:livekit::room - disconnected from room with reason: ClientInitiated", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.7143648, "level": "ERROR", "logger": "livekit", "module": "_utils", "function": "task_done_logger", "line": 39, "message": "task exception: <Task finished name='Task-25' coro=<AudioStream._run() done, defined at /usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py:252> exception=AttributeError(\"'AudioStream' object has no attribute '_ffi_handle'\")>", "exc_info": "Traceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 254, in _run\n    event = await self._ffi_queue.wait_for(self._is_event)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_utils.py\", line 82, in wait_for\n    if fnc(event):\n       ^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 278, in _is_event\n    return e.audio_stream_event.stream_handle == self._ffi_handle.handle\n                                                 ^^^^^^^^^^^^^^^^\nAttributeError: 'AudioStream' object has no attribute '_ffi_handle'", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.7143648, "level": "ERROR", "logger": "livekit", "module": "_utils", "function": "task_done_logger", "line": 39, "message": "task exception: <Task finished name='Task-25' coro=<AudioStream._run() done, defined at /usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py:252> exception=AttributeError(\"'AudioStream' object has no attribute '_ffi_handle'\")>\nTraceback (most recent call last):\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 254, in _run\n    event = await self._ffi_queue.wait_for(self._is_event)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/_utils.py\", line 82, in wait_for\n    if fnc(event):\n       ^^^^^^^^^^\n  File \"/usr/local/lib/python3.12/site-packages/livekit/rtc/audio_stream.py\", line 278, in _is_event\n    return e.audio_stream_event.stream_handle == self._ffi_handle.handle\n                                                 ^^^^^^^^^^^^^^^^\nAttributeError: 'AudioStream' object has no attribute '_ffi_handle'", "taskName": null, "pid": 76, "job_id": "AJ_A8BJzed8moPR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.7190688, "level": "DEBUG", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "_run_job_task", "line": 294, "message": "shutting down job task", "taskName": "job_task", "reason": "room disconnected", "user_initiated": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.7207153, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "_read_ipc_task", "line": 315, "message": "process exiting", "taskName": "Task-74", "reason": "room disconnected", "pid": 76, "job_id": "AJ_A8BJzed8moPR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.7223678, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.7233868, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.7223678, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "taskName": "job_shutdown_callback", "pid": 76, "job_id": "AJ_A8BJzed8moPR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.724592, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "taskName": "job_shutdown_callback", "event": "application_shutdown", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.7233868, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "taskName": "job_shutdown_callback", "pid": 76, "job_id": "AJ_A8BJzed8moPR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.7260547, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_close_http_ctx", "line": 53, "message": "http_session(): closing the httpclient ctx", "taskName": "job_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.724592, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "taskName": "job_shutdown_callback", "event": "application_shutdown", "component": "database", "status": "success", "pid": 76, "job_id": "AJ_A8BJzed8moPR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016347.7270532, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_new_session", "line": 20, "message": "http_session(): creating a new httpclient ctx", "taskName": "job_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016430.2195997, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "drain", "line": 502, "message": "draining worker", "taskName": "Task-199", "id": "AW_RL8BMBDjUjdp", "timeout": 1800, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755016430.2277617, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "aclose", "line": 585, "message": "shutting down worker", "taskName": "Task-200", "id": "AW_RL8BMBDjUjdp", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017281.3848157, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 389, "message": "starting worker", "version": "1.2.5", "rtc-version": "1.0.12", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017281.3859758, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "preloading plugins", "packages": ["livekit.plugins.elevenlabs", "livekit.plugins.openai", "livekit.plugins.silero", "livekit.plugins.deepgram", "livekit.plugins.google", "av"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017283.8216352, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 42, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017283.828726, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 44, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017283.837997, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 46, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017283.8497338, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 48, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017285.0642636, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017285.0642636, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017285.0653896, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017285.0664582, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 42, "elapsed_time": 1.24, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017285.0659118, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017285.070331, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 46, "elapsed_time": 1.23, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017285.0757985, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 48, "elapsed_time": 1.22, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017285.078423, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 44, "elapsed_time": 1.25, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017286.2089708, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 799, "message": "registered worker", "id": "AW_mcow9KsMBFgd", "url": "wss://salon-dev-z1d1tpn4.livekit.cloud", "region": "US West B", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017286.4793541, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 870, "message": "received job request", "job_id": "AJ_2taSuZJsmmjc", "dispatch_id": "", "room_name": "playground-7NOb-EKGt", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017286.7366498, "level": "DEBUG", "logger": "tortoise", "module": "__init__", "function": "init", "line": 505, "message": "Tortoise-ORM startup\n    connections: {'default': {'engine': 'tortoise.backends.asyncpg', 'credentials': {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'password': 'uhmpBp***', 'database': 'postgres', 'minsize': 1, 'maxsize': 10, 'command_timeout': 60}}}\n    apps: {'models': {'models': ['models.customer', 'models.livekit', 'models.staff', 'models.service', 'models.appointment', 'models.recording', 'aerich.models'], 'default_connection': 'default'}}", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017286.8528178, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 108, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017286.8895686, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017286.8908083, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017286.8895686, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "pid": 42, "job_id": "AJ_2taSuZJsmmjc", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017286.8908083, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "event": "application_startup", "component": "database", "status": "success", "pid": 42, "job_id": "AJ_2taSuZJsmmjc", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017286.941509, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_api::signal_client::signal_stream:106:livekit_api::signal_client::signal_stream - connecting to wss://salon-dev-z1d1tpn4.livekit.cloud/rtc?sdk=python&protocol=16&auto_subscribe=1&adaptive_stream=0&version=1.0.12&access_token=...", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017287.4833672, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::anchors:150:rustls::anchors - add_parsable_certificates processed 142 valid and 0 invalid certs", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017287.4912484, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tokio_tungstenite::tls::encryption::rustls:103:tokio_tungstenite::tls::encryption::rustls - Added 142/142 native root certificates (ignored 0)", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017287.4997385, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:73:rustls::client::hs - No cached session for DnsName(\"salon-dev-z1d1tpn4.livekit.cloud\")", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017287.5057907, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:132:rustls::client::hs - Not resuming any session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017287.8806512, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:615:rustls::client::hs - Using ciphersuite TLS13_AES_128_GCM_SHA256", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017287.8843277, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:142:rustls::client::tls13 - Not resuming", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017287.8886395, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:381:rustls::client::tls13 - TLS1.3 encrypted extensions: []", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017287.891143, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:472:rustls::client::hs - ALPN protocol is None", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017288.2392638, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::handshake::client:95:tungstenite::handshake::client - Client handshake done.", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017289.1598577, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 108, "elapsed_time": 2.31, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017289.1598732, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017294.6460462, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "on_attached", "line": 65, "message": "input stream attached", "participant": null, "source": "SOURCE_UNKNOWN", "accepted_sources": ["SOURCE_MICROPHONE"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017295.0357025, "level": "DEBUG", "logger": "livekit.plugins.google", "module": "realtime_api", "function": "_main_task", "line": 588, "message": "connecting to Gemini Realtime API...", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.2500541, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "_forward_task", "line": 141, "message": "start reading stream", "participant": "identity-bREO", "source": "SOURCE_MICROPHONE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.255887, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "_forward_task", "line": 148, "message": "stream closed", "participant": "identity-bREO", "source": "SOURCE_MICROPHONE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.2573414, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "start", "line": 546, "message": "using audio io: `RoomIO` -> `AgentSession` -> `TranscriptSynchronizer` -> `RoomIO`", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.258045, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "start", "line": 552, "message": "using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `RoomIO`", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.2590075, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x7f891d83e780>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.2590075, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x7f891d83e780>", "pid": 42, "job_id": "AJ_2taSuZJsmmjc", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.277732, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CONNECTING", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.3342252, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 310, "message": "> GET //ws/google.ai.generativelanguage.v1beta.GenerativeService.BidiGenerateContent HTTP/1.1", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.3355856, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Host: generativelanguage.googleapis.com", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.3364902, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Upgrade: websocket", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.337773, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Connection: Upgrade", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.33892, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Key: wOlbxN/SU3STXVYaXsjMbg==", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.3398669, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Version: 13", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.340906, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.3415709, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Content-Type: application/json", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.3425987, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-key: AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.3433356, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> user-agent: google-genai-sdk/1.29.0 gl-python/3.12.11", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.34412, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-client: google-genai-sdk/1.29.0 gl-python/3.12.11", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.3449793, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> User-Agent: Python/3.12 websockets/13.1", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.552646, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::protocol:666:tungstenite::protocol - Received close frame: None", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.5593169, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit::room:1388:livekit::room - disconnected from room with reason: ClientInitiated", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6135972, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "parse", "line": 333, "message": "< HTTP/1.1 101 Switching Protocols", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6145742, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "parse", "line": 335, "message": "< X-Client-Wire-Protocol: HTTP/1.1", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6154213, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "parse", "line": 335, "message": "< Connection: Upgrade", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6167142, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "parse", "line": 335, "message": "< Upgrade: websocket", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6178274, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "parse", "line": 335, "message": "< Sec-WebSocket-Accept: Qivr2JdyPMlgDxEHatPmg9IEiVY=", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.618843, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "parse", "line": 335, "message": "< Date: Tue, 12 Aug 2025 16:48:16 GMT", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6196961, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "parse", "line": 335, "message": "< Server-Timing: gfet4t7; dur=233", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6204743, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "parse", "line": 335, "message": "< Set-Cookie: S==YBBJ-LPPKlGtW6JuwbdGpkTk4JvEY__fSt8UmqWIfZU; path=/; priority=low", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6214416, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "parse", "line": 335, "message": "< Alt-Svc: h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6222517, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is OPEN", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6234016, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "send_frame", "line": 719, "message": "> TEXT '{\"setup\": {\"model\": \"models/gemini-2.5-flash-li...dioTranscription\": {}}}' [8291 bytes]", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6255574, "level": "DEBUG", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "_run_job_task", "line": 278, "message": "shutting down job task", "reason": "", "user_initiated": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6266985, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "_read_ipc_task", "line": 318, "message": "process exiting", "reason": "", "pid": 42, "job_id": "AJ_2taSuZJsmmjc", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6274343, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.628174, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6274343, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "pid": 42, "job_id": "AJ_2taSuZJsmmjc", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6290917, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "event": "application_shutdown", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.628174, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "pid": 42, "job_id": "AJ_2taSuZJsmmjc", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.630107, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "on_detached", "line": 78, "message": "input stream detached", "participant": "identity-bREO", "source": "SOURCE_MICROPHONE", "accepted_sources": ["SOURCE_MICROPHONE"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6290917, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "event": "application_shutdown", "component": "database", "status": "success", "pid": 42, "job_id": "AJ_2taSuZJsmmjc", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6312642, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "send_frame", "line": 719, "message": "> CLOSE 1000 (OK) [2 bytes]", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.6324139, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CLOSING", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.9102263, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "parse", "line": 572, "message": "< CLOSE 1000 (OK) [2 bytes]", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.9117525, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "discard", "line": 621, "message": "< EOF", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.912557, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "send_eof", "line": 731, "message": "> EOF", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.913283, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CLOSED", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.9143863, "level": "DEBUG", "logger": "websockets.client", "module": "connection", "function": "send_data", "line": 900, "message": "x closing TCP connection", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e60529f0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.9160438, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "_aclose_impl", "line": 654, "message": "session closed", "reason": "job_shutdown", "error": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.917941, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_close_http_ctx", "line": 57, "message": "http_session(): closing the httpclient ctx", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017296.9188595, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_new_session", "line": 20, "message": "http_session(): creating a new httpclient ctx", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017351.4356818, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 870, "message": "received job request", "job_id": "AJ_LVNjQunnPB8J", "dispatch_id": "", "room_name": "playground-E3sa-zcO6", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017351.70451, "level": "DEBUG", "logger": "tortoise", "module": "__init__", "function": "init", "line": 505, "message": "Tortoise-ORM startup\n    connections: {'default': {'engine': 'tortoise.backends.asyncpg', 'credentials': {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'password': 'uhmpBp***', 'database': 'postgres', 'minsize': 1, 'maxsize': 10, 'command_timeout': 60}}}\n    apps: {'models': {'models': ['models.customer', 'models.livekit', 'models.staff', 'models.service', 'models.appointment', 'models.recording', 'aerich.models'], 'default_connection': 'default'}}", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017351.750766, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 163, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017351.7719142, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017351.7731442, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017351.7719142, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "pid": 46, "job_id": "AJ_LVNjQunnPB8J", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017351.7731442, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 41, "message": "Salon AI Application started successfully", "event": "application_startup", "component": "database", "status": "success", "pid": 46, "job_id": "AJ_LVNjQunnPB8J", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017351.7843072, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_api::signal_client::signal_stream:106:livekit_api::signal_client::signal_stream - connecting to wss://salon-dev-z1d1tpn4.livekit.cloud/rtc?sdk=python&protocol=16&auto_subscribe=1&adaptive_stream=0&version=1.0.12&access_token=...", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017352.0831664, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::anchors:150:rustls::anchors - add_parsable_certificates processed 142 valid and 0 invalid certs", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017352.084273, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tokio_tungstenite::tls::encryption::rustls:103:tokio_tungstenite::tls::encryption::rustls - Added 142/142 native root certificates (ignored 0)", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017352.0860922, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:73:rustls::client::hs - No cached session for DnsName(\"salon-dev-z1d1tpn4.livekit.cloud\")", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017352.087673, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:132:rustls::client::hs - Not resuming any session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017352.3562546, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:615:rustls::client::hs - Using ciphersuite TLS13_AES_128_GCM_SHA256", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017352.3588114, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:142:rustls::client::tls13 - Not resuming", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017352.3601918, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:381:rustls::client::tls13 - TLS1.3 encrypted extensions: []", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017352.3612278, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:472:rustls::client::hs - ALPN protocol is None", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017352.6181223, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::handshake::client:95:tungstenite::handshake::client - Client handshake done.", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017352.6895833, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 163, "elapsed_time": 0.94, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017352.689595, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017359.2033644, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "on_attached", "line": 65, "message": "input stream attached", "participant": null, "source": "SOURCE_UNKNOWN", "accepted_sources": ["SOURCE_MICROPHONE"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017359.4615223, "level": "DEBUG", "logger": "livekit.plugins.google", "module": "realtime_api", "function": "_main_task", "line": 588, "message": "connecting to Gemini Realtime API...", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.3994331, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "_forward_task", "line": 141, "message": "start reading stream", "participant": "identity-ZxdR", "source": "SOURCE_MICROPHONE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.4049742, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "_forward_task", "line": 148, "message": "stream closed", "participant": "identity-ZxdR", "source": "SOURCE_MICROPHONE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.4063594, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "start", "line": 546, "message": "using audio io: `RoomIO` -> `AgentSession` -> `TranscriptSynchronizer` -> `RoomIO`", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.4072473, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "start", "line": 552, "message": "using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `RoomIO`", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.408504, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x7f88fce3aa80>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.408504, "level": "ERROR", "logger": "asyncio", "module": "base_events", "function": "default_exception_handler", "line": 1833, "message": "Unclosed client session\nclient_session: <aiohttp.client.ClientSession object at 0x7f88fce3aa80>", "pid": 46, "job_id": "AJ_LVNjQunnPB8J", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.4324586, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CONNECTING", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.496341, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 310, "message": "> GET //ws/google.ai.generativelanguage.v1beta.GenerativeService.BidiGenerateContent HTTP/1.1", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e97a93a0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.5010252, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Host: generativelanguage.googleapis.com", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e97a93a0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.503032, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Upgrade: websocket", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e97a93a0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.5051324, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Connection: Upgrade", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e97a93a0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.5066173, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Key: qoF/dT6WYzJf8PQtvqAuHQ==", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e97a93a0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.5087912, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Version: 13", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e97a93a0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.510338, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e97a93a0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.5115104, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Content-Type: application/json", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e97a93a0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.5128157, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-key: AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e97a93a0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.5140436, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> user-agent: google-genai-sdk/1.29.0 gl-python/3.12.11", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e97a93a0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.5160644, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-client: google-genai-sdk/1.29.0 gl-python/3.12.11", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e97a93a0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.517307, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> User-Agent: Python/3.12 websockets/13.1", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f88e97a93a0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.6585236, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::protocol:666:tungstenite::protocol - Received close frame: None", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.6607323, "level": "DEBUG", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "_run_job_task", "line": 278, "message": "shutting down job task", "reason": "", "user_initiated": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.662758, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "_read_ipc_task", "line": 318, "message": "process exiting", "reason": "", "pid": 46, "job_id": "AJ_LVNjQunnPB8J", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.666237, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.666237, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "pid": 46, "job_id": "AJ_LVNjQunnPB8J", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.6674192, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.6689336, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit::room:1388:livekit::room - disconnected from room with reason: ClientInitiated", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.6674192, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "pid": 46, "job_id": "AJ_LVNjQunnPB8J", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.669303, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "event": "application_shutdown", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.6709101, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "on_detached", "line": 78, "message": "input stream detached", "participant": "identity-ZxdR", "source": "SOURCE_MICROPHONE", "accepted_sources": ["SOURCE_MICROPHONE"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.669303, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 60, "message": "Salon AI Application shutdown complete", "event": "application_shutdown", "component": "database", "status": "success", "pid": 46, "job_id": "AJ_LVNjQunnPB8J", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.673895, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "_aclose_impl", "line": 654, "message": "session closed", "reason": "job_shutdown", "error": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.675187, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_close_http_ctx", "line": 57, "message": "http_session(): closing the httpclient ctx", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017361.6762915, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_new_session", "line": 20, "message": "http_session(): creating a new httpclient ctx", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017637.8161094, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 389, "message": "starting worker", "taskName": "agent_runner", "version": "1.2.5", "rtc-version": "1.0.12", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017637.8262885, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 151, "message": "initializing job runner", "taskName": "Task-5", "tid": 32188, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017637.8274808, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 151, "message": "initializing job runner", "taskName": "Task-6", "tid": 43724, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017637.828486, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 151, "message": "initializing job runner", "taskName": "Task-7", "tid": 13604, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017637.8298156, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 151, "message": "initializing job runner", "taskName": "Task-8", "tid": 32740, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017637.831945, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 160, "message": "job runner initialized", "taskName": "Task-5", "tid": 32188, "elapsed_time": 0.0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017637.8342824, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 160, "message": "job runner initialized", "taskName": "Task-6", "tid": 43724, "elapsed_time": 0.01, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017637.8363452, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 160, "message": "job runner initialized", "taskName": "Task-8", "tid": 32740, "elapsed_time": 0.01, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017637.8374124, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 160, "message": "job runner initialized", "taskName": "Task-7", "tid": 13604, "elapsed_time": 0.01, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017638.9815395, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 799, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_ShY3Dc8bBTT2", "url": "wss://salon-dev-z1d1tpn4.livekit.cloud", "region": "US West B", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017653.614503, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 870, "message": "received job request", "taskName": "Task-53", "job_id": "AJ_gwDRjDjp6xXC", "dispatch_id": "", "room_name": "playground-AZkv-NkOb", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017653.9524012, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017653.9524012, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 72, "message": "Salon AI Application started successfully", "taskName": "job_user_entrypoint", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017653.9565508, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 151, "message": "initializing job runner", "taskName": "Task-58", "tid": 43464, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017653.9565508, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 160, "message": "job runner initialized", "taskName": "Task-58", "tid": 43464, "elapsed_time": 0.0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017657.6082306, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "drain", "line": 492, "message": "draining worker", "id": "AW_mcow9KsMBFgd", "timeout": 1800, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017657.623896, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "aclose", "line": 575, "message": "shutting down worker", "id": "AW_mcow9KsMBFgd", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017661.5292478, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_update_worker_status", "line": 947, "message": "worker is at full capacity, marking as unavailable", "taskName": "Task-50", "load": 0.835, "threshold": "_WorkerEnvOption(dev_default=inf, prod_default=0.7)", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017664.031631, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_update_worker_status", "line": 952, "message": "worker is below capacity, marking as available", "taskName": "Task-50", "load": 0.475, "threshold": "_WorkerEnvOption(dev_default=inf, prod_default=0.7)", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017665.970498, "level": "WARNING", "logger": "livekit.agents", "module": "debug", "function": "instrumented", "line": 19, "message": "Running <Task pending name='job_shutdown_callback' coro=<JobContext.add_shutdown_callback.<locals>.wrapper() running at D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\job.py:234> cb=[gather.<locals>._done_callback() at C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.12_3.12.2800.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\tasks.py:767]> took too long: 2.80 seconds", "taskName": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017665.9714358, "level": "WARNING", "logger": "salon_ai.main", "module": "main", "function": "cleanup_aiohttp_sessions", "line": 53, "message": "Error closing aiohttp session: Task <Task pending name='job_shutdown_callback' coro=<JobContext.add_shutdown_callback.<locals>.wrapper() running at D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\job.py:234> cb=[gather.<locals>._done_callback() at C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.12_3.12.2800.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\tasks.py:767]> got Future <Task pending name='Task-92' coro=<_wait_for_close() running at D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\connector.py:137> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]>> attached to a different loop", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017665.9806843, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017665.9806843, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017665.9830306, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 91, "message": "Salon AI Application shutdown complete", "taskName": "job_shutdown_callback", "event": "application_shutdown", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017666.2192442, "level": "WARNING", "logger": "livekit.agents", "module": "worker", "function": "_connection_task", "line": 700, "message": "failed to connect to livekit, retrying in 0s", "exc_info": "Traceback (most recent call last):\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\worker.py\", line 687, in _connection_task\n    await self._run_ws(ws)\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\worker.py\", line 768, in _run_ws\n    await asyncio.gather(*tasks)\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\worker.py\", line 740, in _recv_task\n    raise Exception(\"worker connection closed unexpectedly\")\nException: worker connection closed unexpectedly", "taskName": "worker_conn_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017666.2222433, "level": "WARNING", "logger": "livekit.agents", "module": "worker", "function": "_connection_task", "line": 700, "message": "failed to connect to livekit, retrying in 2s", "exc_info": "Traceback (most recent call last):\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\worker.py\", line 648, in _connection_task\n    ws = await self._http_session.ws_connect(\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 1048, in _ws_connect\n    resp = await self.request(\n           ^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 517, in _request\n    raise RuntimeError(\"Session is closed\")\nRuntimeError: Session is closed", "taskName": "worker_conn_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017668.233055, "level": "WARNING", "logger": "livekit.agents", "module": "worker", "function": "_connection_task", "line": 700, "message": "failed to connect to livekit, retrying in 4s", "exc_info": "Traceback (most recent call last):\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\worker.py\", line 648, in _connection_task\n    ws = await self._http_session.ws_connect(\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 1048, in _ws_connect\n    resp = await self.request(\n           ^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 517, in _request\n    raise RuntimeError(\"Session is closed\")\nRuntimeError: Session is closed", "taskName": "worker_conn_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017672.231061, "level": "WARNING", "logger": "livekit.agents", "module": "worker", "function": "_connection_task", "line": 700, "message": "failed to connect to livekit, retrying in 6s", "exc_info": "Traceback (most recent call last):\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\worker.py\", line 648, in _connection_task\n    ws = await self._http_session.ws_connect(\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 1048, in _ws_connect\n    resp = await self.request(\n           ^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 517, in _request\n    raise RuntimeError(\"Session is closed\")\nRuntimeError: Session is closed", "taskName": "worker_conn_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017678.2500603, "level": "WARNING", "logger": "livekit.agents", "module": "worker", "function": "_connection_task", "line": 700, "message": "failed to connect to livekit, retrying in 8s", "exc_info": "Traceback (most recent call last):\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\worker.py\", line 648, in _connection_task\n    ws = await self._http_session.ws_connect(\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 1048, in _ws_connect\n    resp = await self.request(\n           ^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 517, in _request\n    raise RuntimeError(\"Session is closed\")\nRuntimeError: Session is closed", "taskName": "worker_conn_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017686.2382689, "level": "WARNING", "logger": "livekit.agents", "module": "worker", "function": "_connection_task", "line": 700, "message": "failed to connect to livekit, retrying in 10s", "exc_info": "Traceback (most recent call last):\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\worker.py\", line 648, in _connection_task\n    ws = await self._http_session.ws_connect(\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 1048, in _ws_connect\n    resp = await self.request(\n           ^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 517, in _request\n    raise RuntimeError(\"Session is closed\")\nRuntimeError: Session is closed", "taskName": "worker_conn_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017696.2424579, "level": "WARNING", "logger": "livekit.agents", "module": "worker", "function": "_connection_task", "line": 700, "message": "failed to connect to livekit, retrying in 10s", "exc_info": "Traceback (most recent call last):\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\worker.py\", line 648, in _connection_task\n    ws = await self._http_session.ws_connect(\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 1048, in _ws_connect\n    resp = await self.request(\n           ^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 517, in _request\n    raise RuntimeError(\"Session is closed\")\nRuntimeError: Session is closed", "taskName": "worker_conn_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017706.2521229, "level": "WARNING", "logger": "livekit.agents", "module": "worker", "function": "_connection_task", "line": 700, "message": "failed to connect to livekit, retrying in 10s", "exc_info": "Traceback (most recent call last):\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\worker.py\", line 648, in _connection_task\n    ws = await self._http_session.ws_connect(\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 1048, in _ws_connect\n    resp = await self.request(\n           ^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 517, in _request\n    raise RuntimeError(\"Session is closed\")\nRuntimeError: Session is closed", "taskName": "worker_conn_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017716.2436635, "level": "WARNING", "logger": "livekit.agents", "module": "worker", "function": "_connection_task", "line": 700, "message": "failed to connect to livekit, retrying in 10s", "exc_info": "Traceback (most recent call last):\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\worker.py\", line 648, in _connection_task\n    ws = await self._http_session.ws_connect(\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 1048, in _ws_connect\n    resp = await self.request(\n           ^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 517, in _request\n    raise RuntimeError(\"Session is closed\")\nRuntimeError: Session is closed", "taskName": "worker_conn_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017726.2441025, "level": "WARNING", "logger": "livekit.agents", "module": "worker", "function": "_connection_task", "line": 700, "message": "failed to connect to livekit, retrying in 10s", "exc_info": "Traceback (most recent call last):\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\worker.py\", line 648, in _connection_task\n    ws = await self._http_session.ws_connect(\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 1048, in _ws_connect\n    resp = await self.request(\n           ^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 517, in _request\n    raise RuntimeError(\"Session is closed\")\nRuntimeError: Session is closed", "taskName": "worker_conn_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017736.2514443, "level": "WARNING", "logger": "livekit.agents", "module": "worker", "function": "_connection_task", "line": 700, "message": "failed to connect to livekit, retrying in 10s", "exc_info": "Traceback (most recent call last):\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\worker.py\", line 648, in _connection_task\n    ws = await self._http_session.ws_connect(\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 1048, in _ws_connect\n    resp = await self.request(\n           ^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 517, in _request\n    raise RuntimeError(\"Session is closed\")\nRuntimeError: Session is closed", "taskName": "worker_conn_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017746.2455492, "level": "WARNING", "logger": "livekit.agents", "module": "worker", "function": "_connection_task", "line": 700, "message": "failed to connect to livekit, retrying in 10s", "exc_info": "Traceback (most recent call last):\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\worker.py\", line 648, in _connection_task\n    ws = await self._http_session.ws_connect(\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 1048, in _ws_connect\n    resp = await self.request(\n           ^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 517, in _request\n    raise RuntimeError(\"Session is closed\")\nRuntimeError: Session is closed", "taskName": "worker_conn_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017756.2396529, "level": "WARNING", "logger": "livekit.agents", "module": "worker", "function": "_connection_task", "line": 700, "message": "failed to connect to livekit, retrying in 10s", "exc_info": "Traceback (most recent call last):\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\worker.py\", line 648, in _connection_task\n    ws = await self._http_session.ws_connect(\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 1048, in _ws_connect\n    resp = await self.request(\n           ^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 517, in _request\n    raise RuntimeError(\"Session is closed\")\nRuntimeError: Session is closed", "taskName": "worker_conn_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017766.2453113, "level": "WARNING", "logger": "livekit.agents", "module": "worker", "function": "_connection_task", "line": 700, "message": "failed to connect to livekit, retrying in 10s", "exc_info": "Traceback (most recent call last):\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\worker.py\", line 648, in _connection_task\n    ws = await self._http_session.ws_connect(\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 1048, in _ws_connect\n    resp = await self.request(\n           ^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 517, in _request\n    raise RuntimeError(\"Session is closed\")\nRuntimeError: Session is closed", "taskName": "worker_conn_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017776.2406092, "level": "WARNING", "logger": "livekit.agents", "module": "worker", "function": "_connection_task", "line": 700, "message": "failed to connect to livekit, retrying in 10s", "exc_info": "Traceback (most recent call last):\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\worker.py\", line 648, in _connection_task\n    ws = await self._http_session.ws_connect(\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 1048, in _ws_connect\n    resp = await self.request(\n           ^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 517, in _request\n    raise RuntimeError(\"Session is closed\")\nRuntimeError: Session is closed", "taskName": "worker_conn_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017786.2454154, "level": "WARNING", "logger": "livekit.agents", "module": "worker", "function": "_connection_task", "line": 700, "message": "failed to connect to livekit, retrying in 10s", "exc_info": "Traceback (most recent call last):\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\worker.py\", line 648, in _connection_task\n    ws = await self._http_session.ws_connect(\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 1048, in _ws_connect\n    resp = await self.request(\n           ^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\aiohttp\\client.py\", line 517, in _request\n    raise RuntimeError(\"Session is closed\")\nRuntimeError: Session is closed", "taskName": "worker_conn_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017796.2432446, "level": "ERROR", "logger": "livekit.agents", "module": "log", "function": "async_fn_logs", "line": 21, "message": "Error in _connection_task", "exc_info": "Traceback (most recent call last):\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\utils\\log.py\", line 16, in async_fn_logs\n    return await fn(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\worker.py\", line 693, in _connection_task\n    raise RuntimeError(\nRuntimeError: failed to connect to livekit after 16 attempts", "taskName": "worker_conn_task", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017796.2474504, "level": "ERROR", "logger": "livekit.agents", "module": "_run", "function": "_worker_run", "line": 81, "message": "worker failed", "exc_info": "Traceback (most recent call last):\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\cli\\_run.py\", line 79, in _worker_run\n    await worker.run()\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\worker.py\", line 473, in run\n    await asyncio.gather(*tasks)\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\utils\\log.py\", line 16, in async_fn_logs\n    return await fn(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\Work\\ElectronikMedia\\Salon AI\\voice-agent\\.venv\\Lib\\site-packages\\livekit\\agents\\worker.py\", line 693, in _connection_task\n    raise RuntimeError(\nRuntimeError: failed to connect to livekit after 16 attempts", "taskName": "agent_runner", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017796.2496235, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "drain", "line": 492, "message": "draining worker", "taskName": "Task-104", "id": "AW_ShY3Dc8bBTT2", "timeout": 1800, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017796.2506204, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "aclose", "line": 575, "message": "shutting down worker", "taskName": "Task-105", "id": "AW_ShY3Dc8bBTT2", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017844.305883, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 389, "message": "starting worker", "taskName": "agent_runner", "version": "1.2.5", "rtc-version": "1.0.12", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017844.3119826, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 151, "message": "initializing job runner", "taskName": "Task-5", "tid": 16532, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017844.3129816, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 151, "message": "initializing job runner", "taskName": "Task-6", "tid": 42692, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017844.3129816, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 151, "message": "initializing job runner", "taskName": "Task-7", "tid": 32188, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017844.3139834, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 151, "message": "initializing job runner", "taskName": "Task-8", "tid": 9780, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017844.3165662, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 160, "message": "job runner initialized", "taskName": "Task-5", "tid": 16532, "elapsed_time": 0.0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017844.3183546, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 160, "message": "job runner initialized", "taskName": "Task-6", "tid": 42692, "elapsed_time": 0.0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017844.319363, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 160, "message": "job runner initialized", "taskName": "Task-7", "tid": 32188, "elapsed_time": 0.01, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017844.320363, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 160, "message": "job runner initialized", "taskName": "Task-8", "tid": 9780, "elapsed_time": 0.01, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017845.4060023, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 799, "message": "registered worker", "taskName": "worker_conn_task", "id": "AW_9kAEaiHVothu", "url": "wss://salon-dev-z1d1tpn4.livekit.cloud", "region": "US West B", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017857.04602, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 870, "message": "received job request", "taskName": "Task-52", "job_id": "AJ_sXAXMXmrk5QC", "dispatch_id": "", "room_name": "playground-4uDo-Wq82", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017857.343793, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 151, "message": "initializing job runner", "taskName": "Task-57", "tid": 41692, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017857.3471231, "level": "INFO", "logger": "livekit.agents", "module": "job_thread_executor", "function": "initialize", "line": 160, "message": "job runner initialized", "taskName": "Task-57", "tid": 41692, "elapsed_time": 0.0, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017857.434708, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "taskName": "job_user_entrypoint", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017857.434708, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 42, "message": "Salon AI Application started successfully", "taskName": "job_user_entrypoint", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017865.9806886, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017865.9806886, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "taskName": "job_shutdown_callback", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755017865.9806886, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 61, "message": "Salon AI Application shutdown complete", "taskName": "job_shutdown_callback", "event": "application_shutdown", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018020.788351, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "drain", "line": 492, "message": "draining worker", "taskName": "Task-102", "id": "AW_9kAEaiHVothu", "timeout": 1800, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018020.7893517, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "aclose", "line": 575, "message": "shutting down worker", "taskName": "Task-103", "id": "AW_9kAEaiHVothu", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018227.0514271, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 389, "message": "starting worker", "version": "1.2.5", "rtc-version": "1.0.12", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018227.0537598, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "preloading plugins", "packages": ["livekit.plugins.elevenlabs", "livekit.plugins.openai", "livekit.plugins.silero", "livekit.plugins.deepgram", "livekit.plugins.google", "av"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018229.3598282, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 43, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018229.3623834, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 45, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018229.3653188, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 47, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018229.3679516, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 49, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018230.4628067, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018230.4643743, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 49, "elapsed_time": 1.09, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018230.48405, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 47, "elapsed_time": 1.12, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018230.4840167, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018230.484524, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018230.4859328, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 45, "elapsed_time": 1.12, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018230.5251296, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 43, "elapsed_time": 1.16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018230.5250823, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018231.6150637, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 799, "message": "registered worker", "id": "AW_FKjjqRkSNJMF", "url": "wss://salon-dev-z1d1tpn4.livekit.cloud", "region": "US West B", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018231.8849084, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 870, "message": "received job request", "job_id": "AJ_JxLa7hDEqnLy", "dispatch_id": "", "room_name": "playground-R0Di-csT7", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018232.1336095, "level": "DEBUG", "logger": "tortoise", "module": "__init__", "function": "init", "line": 505, "message": "Tortoise-ORM startup\n    connections: {'default': {'engine': 'tortoise.backends.asyncpg', 'credentials': {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'password': 'uhmpBp***', 'database': 'postgres', 'minsize': 1, 'maxsize': 10, 'command_timeout': 60}}}\n    apps: {'models': {'models': ['models.customer', 'models.livekit', 'models.staff', 'models.service', 'models.appointment', 'models.recording', 'aerich.models'], 'default_connection': 'default'}}", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018232.1989892, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 109, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018232.2379453, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018232.239019, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 42, "message": "Salon AI Application started successfully", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018232.2379453, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "pid": 49, "job_id": "AJ_JxLa7hDEqnLy", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018232.239019, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 42, "message": "Salon AI Application started successfully", "event": "application_startup", "component": "database", "status": "success", "pid": 49, "job_id": "AJ_JxLa7hDEqnLy", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018232.2617214, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_api::signal_client::signal_stream:106:livekit_api::signal_client::signal_stream - connecting to wss://salon-dev-z1d1tpn4.livekit.cloud/rtc?sdk=python&protocol=16&auto_subscribe=1&adaptive_stream=0&version=1.0.12&access_token=...", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018232.5841188, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::anchors:150:rustls::anchors - add_parsable_certificates processed 142 valid and 0 invalid certs", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018232.58507, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tokio_tungstenite::tls::encryption::rustls:103:tokio_tungstenite::tls::encryption::rustls - Added 142/142 native root certificates (ignored 0)", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018232.5858333, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:73:rustls::client::hs - No cached session for DnsName(\"salon-dev-z1d1tpn4.livekit.cloud\")", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018232.5867758, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:132:rustls::client::hs - Not resuming any session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018232.870107, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:615:rustls::client::hs - Using ciphersuite TLS13_AES_128_GCM_SHA256", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018232.8712246, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:142:rustls::client::tls13 - Not resuming", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018232.8727047, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:381:rustls::client::tls13 - TLS1.3 encrypted extensions: []", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018232.8743246, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:472:rustls::client::hs - ALPN protocol is None", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018233.1285388, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::handshake::client:95:tungstenite::handshake::client - Client handshake done.", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018233.2670496, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 109, "elapsed_time": 1.07, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018233.2670224, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018239.14279, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "on_attached", "line": 65, "message": "input stream attached", "participant": null, "source": "SOURCE_UNKNOWN", "accepted_sources": ["SOURCE_MICROPHONE"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018239.497609, "level": "DEBUG", "logger": "livekit.plugins.google", "module": "realtime_api", "function": "_main_task", "line": 588, "message": "connecting to Gemini Realtime API...", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018240.7128017, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "_forward_task", "line": 141, "message": "start reading stream", "participant": "identity-7XTJ", "source": "SOURCE_MICROPHONE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018240.7178872, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "start", "line": 546, "message": "using audio io: `RoomIO` -> `AgentSession` -> `TranscriptSynchronizer` -> `RoomIO`", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018240.7186847, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "start", "line": 552, "message": "using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `RoomIO`", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018240.7200596, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "_forward_task", "line": 148, "message": "stream closed", "participant": "identity-7XTJ", "source": "SOURCE_MICROPHONE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018240.7424152, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CONNECTING", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018240.7902837, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 310, "message": "> GET //ws/google.ai.generativelanguage.v1beta.GenerativeService.BidiGenerateContent HTTP/1.1", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227eeed0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018240.791236, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Host: generativelanguage.googleapis.com", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227eeed0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018240.7929208, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Upgrade: websocket", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227eeed0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018240.7941005, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Connection: Upgrade", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227eeed0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018240.7950974, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Key: XvY1SySkPo7ZTvFXBPqgkA==", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227eeed0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018240.7963707, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Version: 13", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227eeed0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018240.7973874, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227eeed0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018240.7987044, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Content-Type: application/json", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227eeed0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018240.8001387, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-key: AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227eeed0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018240.8011904, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> user-agent: google-genai-sdk/1.29.0 gl-python/3.12.11", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227eeed0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018240.8026726, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-client: google-genai-sdk/1.29.0 gl-python/3.12.11", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227eeed0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018240.8039758, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> User-Agent: Python/3.12 websockets/13.1", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227eeed0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018240.961888, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::protocol:666:tungstenite::protocol - Received close frame: None", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018240.9642005, "level": "DEBUG", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "_run_job_task", "line": 278, "message": "shutting down job task", "reason": "", "user_initiated": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018240.9655678, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "_read_ipc_task", "line": 318, "message": "process exiting", "reason": "", "pid": 49, "job_id": "AJ_JxLa7hDEqnLy", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018240.9733725, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "on_detached", "line": 78, "message": "input stream detached", "participant": "identity-7XTJ", "source": "SOURCE_MICROPHONE", "accepted_sources": ["SOURCE_MICROPHONE"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018240.9756155, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit::room:1388:livekit::room - disconnected from room with reason: ClientInitiated", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018240.9789698, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "_aclose_impl", "line": 654, "message": "session closed", "reason": "job_shutdown", "error": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018241.0697029, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "send_eof", "line": 731, "message": "> EOF", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227eeed0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018241.070869, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "discard", "line": 621, "message": "< EOF", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227eeed0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018241.071892, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CLOSED", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227eeed0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018242.400829, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018242.401919, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018242.400829, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "pid": 49, "job_id": "AJ_JxLa7hDEqnLy", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018242.4029546, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 61, "message": "Salon AI Application shutdown complete", "event": "application_shutdown", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018242.401919, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "pid": 49, "job_id": "AJ_JxLa7hDEqnLy", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018242.404608, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_close_http_ctx", "line": 57, "message": "http_session(): closing the httpclient ctx", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018242.4029546, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 61, "message": "Salon AI Application shutdown complete", "event": "application_shutdown", "component": "database", "status": "success", "pid": 49, "job_id": "AJ_JxLa7hDEqnLy", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018242.4066534, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_new_session", "line": 20, "message": "http_session(): creating a new httpclient ctx", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018269.4880433, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 870, "message": "received job request", "job_id": "AJ_hxd6ToEzMKYY", "dispatch_id": "", "room_name": "playground-tqA0-LGmH", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018269.7578151, "level": "DEBUG", "logger": "tortoise", "module": "__init__", "function": "init", "line": 505, "message": "Tortoise-ORM startup\n    connections: {'default': {'engine': 'tortoise.backends.asyncpg', 'credentials': {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'password': 'uhmpBp***', 'database': 'postgres', 'minsize': 1, 'maxsize': 10, 'command_timeout': 60}}}\n    apps: {'models': {'models': ['models.customer', 'models.livekit', 'models.staff', 'models.service', 'models.appointment', 'models.recording', 'aerich.models'], 'default_connection': 'default'}}", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018269.770067, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 161, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018269.8210764, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018269.822329, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 42, "message": "Salon AI Application started successfully", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018269.8210764, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "pid": 47, "job_id": "AJ_hxd6ToEzMKYY", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018269.822329, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 42, "message": "Salon AI Application started successfully", "event": "application_startup", "component": "database", "status": "success", "pid": 47, "job_id": "AJ_hxd6ToEzMKYY", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018269.831663, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_api::signal_client::signal_stream:106:livekit_api::signal_client::signal_stream - connecting to wss://salon-dev-z1d1tpn4.livekit.cloud/rtc?sdk=python&protocol=16&auto_subscribe=1&adaptive_stream=0&version=1.0.12&access_token=...", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018270.134648, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::anchors:150:rustls::anchors - add_parsable_certificates processed 142 valid and 0 invalid certs", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018270.1363137, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tokio_tungstenite::tls::encryption::rustls:103:tokio_tungstenite::tls::encryption::rustls - Added 142/142 native root certificates (ignored 0)", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018270.1376927, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:73:rustls::client::hs - No cached session for DnsName(\"salon-dev-z1d1tpn4.livekit.cloud\")", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018270.138587, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:132:rustls::client::hs - Not resuming any session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018270.3818188, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:615:rustls::client::hs - Using ciphersuite TLS13_AES_128_GCM_SHA256", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018270.3828697, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:142:rustls::client::tls13 - Not resuming", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018270.3838756, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:381:rustls::client::tls13 - TLS1.3 encrypted extensions: []", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018270.3848002, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:472:rustls::client::hs - ALPN protocol is None", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018270.6351225, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::handshake::client:95:tungstenite::handshake::client - Client handshake done.", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018270.7449882, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 161, "elapsed_time": 0.97, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018270.7449656, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018276.6353848, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "on_attached", "line": 65, "message": "input stream attached", "participant": null, "source": "SOURCE_UNKNOWN", "accepted_sources": ["SOURCE_MICROPHONE"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018276.8918576, "level": "DEBUG", "logger": "livekit.plugins.google", "module": "realtime_api", "function": "_main_task", "line": 588, "message": "connecting to Gemini Realtime API...", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018278.6040518, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "_forward_task", "line": 141, "message": "start reading stream", "participant": "identity-2LV7", "source": "SOURCE_MICROPHONE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018278.60975, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "_forward_task", "line": 148, "message": "stream closed", "participant": "identity-2LV7", "source": "SOURCE_MICROPHONE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018278.6111844, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "start", "line": 546, "message": "using audio io: `RoomIO` -> `AgentSession` -> `TranscriptSynchronizer` -> `RoomIO`", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018278.6120858, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "start", "line": 552, "message": "using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `RoomIO`", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018278.635914, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CONNECTING", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018278.6868498, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 310, "message": "> GET //ws/google.ai.generativelanguage.v1beta.GenerativeService.BidiGenerateContent HTTP/1.1", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227f7e60>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018278.688018, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Host: generativelanguage.googleapis.com", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227f7e60>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018278.6892064, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Upgrade: websocket", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227f7e60>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018278.6905665, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Connection: Upgrade", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227f7e60>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018278.6915548, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Key: ZfPT/MD95RSu+6AFTF+jyA==", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227f7e60>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018278.6927233, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Version: 13", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227f7e60>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018278.6940432, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227f7e60>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018278.6953104, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Content-Type: application/json", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227f7e60>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018278.6968358, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-key: AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227f7e60>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018278.6980581, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> user-agent: google-genai-sdk/1.29.0 gl-python/3.12.11", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227f7e60>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018278.6997504, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-client: google-genai-sdk/1.29.0 gl-python/3.12.11", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227f7e60>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018278.7016163, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> User-Agent: Python/3.12 websockets/13.1", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227f7e60>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018278.8582861, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::protocol:666:tungstenite::protocol - Received close frame: None", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018278.8656735, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit::room:1388:livekit::room - disconnected from room with reason: ClientInitiated", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018278.8679702, "level": "DEBUG", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "_run_job_task", "line": 278, "message": "shutting down job task", "reason": "", "user_initiated": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018278.8695266, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "_read_ipc_task", "line": 318, "message": "process exiting", "reason": "", "pid": 47, "job_id": "AJ_hxd6ToEzMKYY", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018278.8723633, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "on_detached", "line": 78, "message": "input stream detached", "participant": "identity-2LV7", "source": "SOURCE_MICROPHONE", "accepted_sources": ["SOURCE_MICROPHONE"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018278.875806, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "_aclose_impl", "line": 654, "message": "session closed", "reason": "job_shutdown", "error": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018280.0860097, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "send_eof", "line": 731, "message": "> EOF", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227f7e60>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018280.0869763, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "discard", "line": 621, "message": "< EOF", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227f7e60>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018280.0880337, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CLOSED", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f227f7e60>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018280.590558, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018280.591708, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018280.590558, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "pid": 47, "job_id": "AJ_hxd6ToEzMKYY", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018280.5931427, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 61, "message": "Salon AI Application shutdown complete", "event": "application_shutdown", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018280.591708, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "pid": 47, "job_id": "AJ_hxd6ToEzMKYY", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018280.5943997, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_close_http_ctx", "line": 57, "message": "http_session(): closing the httpclient ctx", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018280.5931427, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 61, "message": "Salon AI Application shutdown complete", "event": "application_shutdown", "component": "database", "status": "success", "pid": 47, "job_id": "AJ_hxd6ToEzMKYY", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018280.595437, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_new_session", "line": 20, "message": "http_session(): creating a new httpclient ctx", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018307.1898768, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 870, "message": "received job request", "job_id": "AJ_3dhVP6m4uAEE", "dispatch_id": "", "room_name": "playground-ukmu-eHDn", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018307.4639006, "level": "DEBUG", "logger": "tortoise", "module": "__init__", "function": "init", "line": 505, "message": "Tortoise-ORM startup\n    connections: {'default': {'engine': 'tortoise.backends.asyncpg', 'credentials': {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'password': 'uhmpBp***', 'database': 'postgres', 'minsize': 1, 'maxsize': 10, 'command_timeout': 60}}}\n    apps: {'models': {'models': ['models.customer', 'models.livekit', 'models.staff', 'models.service', 'models.appointment', 'models.recording', 'aerich.models'], 'default_connection': 'default'}}", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018307.5681002, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 210, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018307.5724626, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018307.5732877, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 42, "message": "Salon AI Application started successfully", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018307.5724626, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "pid": 45, "job_id": "AJ_3dhVP6m4uAEE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018307.5732877, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 42, "message": "Salon AI Application started successfully", "event": "application_startup", "component": "database", "status": "success", "pid": 45, "job_id": "AJ_3dhVP6m4uAEE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018307.6074743, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_api::signal_client::signal_stream:106:livekit_api::signal_client::signal_stream - connecting to wss://salon-dev-z1d1tpn4.livekit.cloud/rtc?sdk=python&protocol=16&auto_subscribe=1&adaptive_stream=0&version=1.0.12&access_token=...", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018307.8903024, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::anchors:150:rustls::anchors - add_parsable_certificates processed 142 valid and 0 invalid certs", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018307.8920357, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tokio_tungstenite::tls::encryption::rustls:103:tokio_tungstenite::tls::encryption::rustls - Added 142/142 native root certificates (ignored 0)", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018307.893329, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:73:rustls::client::hs - No cached session for DnsName(\"salon-dev-z1d1tpn4.livekit.cloud\")", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018307.894211, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:132:rustls::client::hs - Not resuming any session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018308.135666, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:615:rustls::client::hs - Using ciphersuite TLS13_AES_128_GCM_SHA256", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018308.1368911, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:142:rustls::client::tls13 - Not resuming", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018308.1380007, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:381:rustls::client::tls13 - TLS1.3 encrypted extensions: []", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018308.1392474, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:472:rustls::client::hs - ALPN protocol is None", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018308.3830993, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::handshake::client:95:tungstenite::handshake::client - Client handshake done.", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018308.8599875, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 210, "elapsed_time": 1.29, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018308.8601546, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018314.4723651, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "on_attached", "line": 65, "message": "input stream attached", "participant": null, "source": "SOURCE_UNKNOWN", "accepted_sources": ["SOURCE_MICROPHONE"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018314.979692, "level": "DEBUG", "logger": "livekit.plugins.google", "module": "realtime_api", "function": "_main_task", "line": 588, "message": "connecting to Gemini Realtime API...", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018316.185555, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "_forward_task", "line": 141, "message": "start reading stream", "participant": "identity-3fPG", "source": "SOURCE_MICROPHONE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018316.1911907, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "start", "line": 546, "message": "using audio io: `RoomIO` -> `AgentSession` -> `TranscriptSynchronizer` -> `RoomIO`", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018316.1920826, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "start", "line": 552, "message": "using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `RoomIO`", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018316.195994, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "_forward_task", "line": 148, "message": "stream closed", "participant": "identity-3fPG", "source": "SOURCE_MICROPHONE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018316.2146115, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CONNECTING", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018316.2675962, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 310, "message": "> GET //ws/google.ai.generativelanguage.v1beta.GenerativeService.BidiGenerateContent HTTP/1.1", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f201de420>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018316.2686887, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Host: generativelanguage.googleapis.com", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f201de420>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018316.2696826, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Upgrade: websocket", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f201de420>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018316.2704651, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Connection: Upgrade", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f201de420>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018316.271165, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Key: AclTjo/FvbU+Mgj2xnTTcA==", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f201de420>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018316.272075, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Version: 13", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f201de420>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018316.2730567, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f201de420>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018316.2738495, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Content-Type: application/json", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f201de420>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018316.2746463, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-key: AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f201de420>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018316.2759833, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> user-agent: google-genai-sdk/1.29.0 gl-python/3.12.11", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f201de420>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018316.277266, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-client: google-genai-sdk/1.29.0 gl-python/3.12.11", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f201de420>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018316.2781734, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> User-Agent: Python/3.12 websockets/13.1", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f201de420>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018316.4318914, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::protocol:666:tungstenite::protocol - Received close frame: None", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018316.4705288, "level": "DEBUG", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "_run_job_task", "line": 278, "message": "shutting down job task", "reason": "", "user_initiated": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018316.4721634, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "_read_ipc_task", "line": 318, "message": "process exiting", "reason": "", "pid": 45, "job_id": "AJ_3dhVP6m4uAEE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018316.4751241, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "on_detached", "line": 78, "message": "input stream detached", "participant": "identity-3fPG", "source": "SOURCE_MICROPHONE", "accepted_sources": ["SOURCE_MICROPHONE"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018316.4773247, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "_aclose_impl", "line": 654, "message": "session closed", "reason": "job_shutdown", "error": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018316.5409462, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "send_eof", "line": 731, "message": "> EOF", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f201de420>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018316.5419183, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "discard", "line": 621, "message": "< EOF", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f201de420>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018316.5427513, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CLOSED", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f201de420>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018317.9361424, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018317.9373276, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018317.9361424, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "pid": 45, "job_id": "AJ_3dhVP6m4uAEE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018317.93852, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 61, "message": "Salon AI Application shutdown complete", "event": "application_shutdown", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018317.9373276, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "pid": 45, "job_id": "AJ_3dhVP6m4uAEE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018317.9405513, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_close_http_ctx", "line": 57, "message": "http_session(): closing the httpclient ctx", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018317.93852, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 61, "message": "Salon AI Application shutdown complete", "event": "application_shutdown", "component": "database", "status": "success", "pid": 45, "job_id": "AJ_3dhVP6m4uAEE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018317.9418721, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_new_session", "line": 20, "message": "http_session(): creating a new httpclient ctx", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018385.5323195, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 870, "message": "received job request", "job_id": "AJ_9X8XxQV7HFmm", "dispatch_id": "", "room_name": "playground-juo1-ylcA", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018385.7802775, "level": "DEBUG", "logger": "tortoise", "module": "__init__", "function": "init", "line": 505, "message": "Tortoise-ORM startup\n    connections: {'default': {'engine': 'tortoise.backends.asyncpg', 'credentials': {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'password': 'uhmpBp***', 'database': 'postgres', 'minsize': 1, 'maxsize': 10, 'command_timeout': 60}}}\n    apps: {'models': {'models': ['models.customer', 'models.livekit', 'models.staff', 'models.service', 'models.appointment', 'models.recording', 'aerich.models'], 'default_connection': 'default'}}", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018385.872943, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 266, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018385.8834114, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018385.884242, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 42, "message": "Salon AI Application started successfully", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018385.8834114, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "pid": 43, "job_id": "AJ_9X8XxQV7HFmm", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018385.884242, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 42, "message": "Salon AI Application started successfully", "event": "application_startup", "component": "database", "status": "success", "pid": 43, "job_id": "AJ_9X8XxQV7HFmm", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018385.9074833, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_api::signal_client::signal_stream:106:livekit_api::signal_client::signal_stream - connecting to wss://salon-dev-z1d1tpn4.livekit.cloud/rtc?sdk=python&protocol=16&auto_subscribe=1&adaptive_stream=0&version=1.0.12&access_token=...", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018386.2372596, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::anchors:150:rustls::anchors - add_parsable_certificates processed 142 valid and 0 invalid certs", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018386.2385864, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tokio_tungstenite::tls::encryption::rustls:103:tokio_tungstenite::tls::encryption::rustls - Added 142/142 native root certificates (ignored 0)", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018386.2396, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:73:rustls::client::hs - No cached session for DnsName(\"salon-dev-z1d1tpn4.livekit.cloud\")", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018386.240631, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:132:rustls::client::hs - Not resuming any session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018386.480755, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:615:rustls::client::hs - Using ciphersuite TLS13_AES_128_GCM_SHA256", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018386.481907, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:142:rustls::client::tls13 - Not resuming", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018386.4833922, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:381:rustls::client::tls13 - TLS1.3 encrypted extensions: []", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018386.48459, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:472:rustls::client::hs - ALPN protocol is None", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018386.7264183, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::handshake::client:95:tungstenite::handshake::client - Client handshake done.", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018386.968454, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 266, "elapsed_time": 1.09, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018386.9683502, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018392.6408174, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "on_attached", "line": 65, "message": "input stream attached", "participant": null, "source": "SOURCE_UNKNOWN", "accepted_sources": ["SOURCE_MICROPHONE"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018392.9459612, "level": "DEBUG", "logger": "livekit.plugins.google", "module": "realtime_api", "function": "_main_task", "line": 588, "message": "connecting to Gemini Realtime API...", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018394.8373253, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "_forward_task", "line": 141, "message": "start reading stream", "participant": "identity-PTF1", "source": "SOURCE_MICROPHONE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018394.8437543, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "_forward_task", "line": 148, "message": "stream closed", "participant": "identity-PTF1", "source": "SOURCE_MICROPHONE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018394.8455462, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "start", "line": 546, "message": "using audio io: `RoomIO` -> `AgentSession` -> `TranscriptSynchronizer` -> `RoomIO`", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018394.8474324, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "start", "line": 552, "message": "using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `RoomIO`", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018394.8613951, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CONNECTING", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018394.905624, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 310, "message": "> GET //ws/google.ai.generativelanguage.v1beta.GenerativeService.BidiGenerateContent HTTP/1.1", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f202389e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018394.9067907, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Host: generativelanguage.googleapis.com", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f202389e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018394.9079883, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Upgrade: websocket", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f202389e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018394.9091892, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Connection: Upgrade", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f202389e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018394.910251, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Key: ff0LNEi/Zt2HyLerZhE5jQ==", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f202389e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018394.9113247, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Version: 13", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f202389e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018394.9123385, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f202389e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018394.9133565, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Content-Type: application/json", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f202389e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018394.9144764, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-key: AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f202389e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018394.9167113, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> user-agent: google-genai-sdk/1.29.0 gl-python/3.12.11", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f202389e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018394.9182189, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-client: google-genai-sdk/1.29.0 gl-python/3.12.11", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f202389e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018394.9192827, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> User-Agent: Python/3.12 websockets/13.1", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f202389e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018395.1111999, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::protocol:666:tungstenite::protocol - Received close frame: None", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018395.1232316, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit::room:1388:livekit::room - disconnected from room with reason: ClientInitiated", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018395.1846097, "level": "DEBUG", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "_run_job_task", "line": 278, "message": "shutting down job task", "reason": "", "user_initiated": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018395.1874728, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "_read_ipc_task", "line": 318, "message": "process exiting", "reason": "", "pid": 43, "job_id": "AJ_9X8XxQV7HFmm", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018395.1934497, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "on_detached", "line": 78, "message": "input stream detached", "participant": "identity-PTF1", "source": "SOURCE_MICROPHONE", "accepted_sources": ["SOURCE_MICROPHONE"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018395.2026422, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "_aclose_impl", "line": 654, "message": "session closed", "reason": "job_shutdown", "error": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018396.3181868, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "send_eof", "line": 731, "message": "> EOF", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f202389e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018396.3192415, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "discard", "line": 621, "message": "< EOF", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f202389e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018396.3206897, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CLOSED", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f0f202389e0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018396.5883293, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018396.5897517, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018396.5883293, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "pid": 43, "job_id": "AJ_9X8XxQV7HFmm", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018396.5907202, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 61, "message": "Salon AI Application shutdown complete", "event": "application_shutdown", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018396.5897517, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "pid": 43, "job_id": "AJ_9X8XxQV7HFmm", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018396.5920079, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_close_http_ctx", "line": 57, "message": "http_session(): closing the httpclient ctx", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018396.5907202, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 61, "message": "Salon AI Application shutdown complete", "event": "application_shutdown", "component": "database", "status": "success", "pid": 43, "job_id": "AJ_9X8XxQV7HFmm", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018396.5933185, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_new_session", "line": 20, "message": "http_session(): creating a new httpclient ctx", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018539.6001918, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "drain", "line": 492, "message": "draining worker", "id": "AW_FKjjqRkSNJMF", "timeout": 1800, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018539.6037536, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "aclose", "line": 575, "message": "shutting down worker", "id": "AW_FKjjqRkSNJMF", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018579.7331243, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 389, "message": "starting worker", "version": "1.2.5", "rtc-version": "1.0.12", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018579.7343822, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "run", "line": 396, "message": "preloading plugins", "packages": ["livekit.plugins.elevenlabs", "livekit.plugins.openai", "livekit.plugins.silero", "livekit.plugins.deepgram", "livekit.plugins.google", "av"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018582.0980763, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 42, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018582.101215, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 44, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018582.104646, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 46, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018582.1086726, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 48, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018582.9988499, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 48, "elapsed_time": 0.89, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018582.9987185, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018582.998897, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018583.0002632, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 46, "elapsed_time": 0.89, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018582.9999547, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018583.0034006, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 42, "elapsed_time": 0.9, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018582.9989111, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018583.0048842, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 44, "elapsed_time": 0.9, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018584.1349022, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_handle_register", "line": 799, "message": "registered worker", "id": "AW_6ZVB2VBVDss7", "url": "wss://salon-dev-z1d1tpn4.livekit.cloud", "region": "US West B", "protocol": 16, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018587.046062, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 870, "message": "received job request", "job_id": "AJ_K6GqJbU36e5e", "dispatch_id": "", "room_name": "playground-yXmg-fhRb", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018587.3054073, "level": "DEBUG", "logger": "tortoise", "module": "__init__", "function": "init", "line": 505, "message": "Tortoise-ORM startup\n    connections: {'default': {'engine': 'tortoise.backends.asyncpg', 'credentials': {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'password': 'uhmpBp***', 'database': 'postgres', 'minsize': 1, 'maxsize': 10, 'command_timeout': 60}}}\n    apps: {'models': {'models': ['models.customer', 'models.livekit', 'models.staff', 'models.service', 'models.appointment', 'models.recording', 'aerich.models'], 'default_connection': 'default'}}", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018587.3951142, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018587.3963196, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 42, "message": "Salon AI Application started successfully", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018587.3951142, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "pid": 48, "job_id": "AJ_K6GqJbU36e5e", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018587.3963196, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 42, "message": "Salon AI Application started successfully", "event": "application_startup", "component": "database", "status": "success", "pid": 48, "job_id": "AJ_K6GqJbU36e5e", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018587.4057102, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 109, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018587.4202874, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_api::signal_client::signal_stream:106:livekit_api::signal_client::signal_stream - connecting to wss://salon-dev-z1d1tpn4.livekit.cloud/rtc?sdk=python&protocol=16&auto_subscribe=1&adaptive_stream=0&version=1.0.12&access_token=...", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018587.695572, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::anchors:150:rustls::anchors - add_parsable_certificates processed 142 valid and 0 invalid certs", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018587.6965117, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tokio_tungstenite::tls::encryption::rustls:103:tokio_tungstenite::tls::encryption::rustls - Added 142/142 native root certificates (ignored 0)", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018587.6975744, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:73:rustls::client::hs - No cached session for DnsName(\"salon-dev-z1d1tpn4.livekit.cloud\")", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018587.6983297, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:132:rustls::client::hs - Not resuming any session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018587.9381807, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:615:rustls::client::hs - Using ciphersuite TLS13_AES_128_GCM_SHA256", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018587.9393175, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:142:rustls::client::tls13 - Not resuming", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018587.9403868, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:381:rustls::client::tls13 - TLS1.3 encrypted extensions: []", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018587.9415886, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:472:rustls::client::hs - ALPN protocol is None", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018588.193625, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::handshake::client:95:tungstenite::handshake::client - Client handshake done.", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018588.2680178, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 109, "elapsed_time": 0.86, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018588.268152, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018594.1359904, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "on_attached", "line": 65, "message": "input stream attached", "participant": null, "source": "SOURCE_UNKNOWN", "accepted_sources": ["SOURCE_MICROPHONE"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018594.485544, "level": "DEBUG", "logger": "livekit.plugins.google", "module": "realtime_api", "function": "_main_task", "line": 588, "message": "connecting to Gemini Realtime API...", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018595.727006, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "_forward_task", "line": 141, "message": "start reading stream", "participant": "identity-66sJ", "source": "SOURCE_MICROPHONE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018595.7389233, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "_forward_task", "line": 148, "message": "stream closed", "participant": "identity-66sJ", "source": "SOURCE_MICROPHONE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018595.7439444, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "start", "line": 546, "message": "using audio io: `RoomIO` -> `AgentSession` -> `TranscriptSynchronizer` -> `RoomIO`", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018595.7449615, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "start", "line": 552, "message": "using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `RoomIO`", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018595.7576525, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CONNECTING", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018595.8077533, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 310, "message": "> GET //ws/google.ai.generativelanguage.v1beta.GenerativeService.BidiGenerateContent HTTP/1.1", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43fcbef380>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018595.8089347, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Host: generativelanguage.googleapis.com", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43fcbef380>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018595.8105223, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Upgrade: websocket", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43fcbef380>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018595.8114543, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Connection: Upgrade", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43fcbef380>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018595.8122594, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Key: 9y435wHjzMSuk//B3mwiYA==", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43fcbef380>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018595.8129573, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Version: 13", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43fcbef380>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018595.8138576, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43fcbef380>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018595.814794, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Content-Type: application/json", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43fcbef380>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018595.8168898, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-key: AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43fcbef380>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018595.8180716, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> user-agent: google-genai-sdk/1.29.0 gl-python/3.12.11", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43fcbef380>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018595.8192978, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-client: google-genai-sdk/1.29.0 gl-python/3.12.11", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43fcbef380>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018595.8200696, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> User-Agent: Python/3.12 websockets/13.1", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43fcbef380>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018595.9936376, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::protocol:666:tungstenite::protocol - Received close frame: None", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018596.0010366, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit::room:1388:livekit::room - disconnected from room with reason: ClientInitiated", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018596.0021143, "level": "DEBUG", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "_run_job_task", "line": 278, "message": "shutting down job task", "reason": "room disconnected", "user_initiated": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018596.005136, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "_read_ipc_task", "line": 318, "message": "process exiting", "reason": "room disconnected", "pid": 48, "job_id": "AJ_K6GqJbU36e5e", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018596.0070093, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "on_detached", "line": 78, "message": "input stream detached", "participant": "identity-66sJ", "source": "SOURCE_MICROPHONE", "accepted_sources": ["SOURCE_MICROPHONE"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018596.0096378, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "_aclose_impl", "line": 654, "message": "session closed", "reason": "job_shutdown", "error": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018597.256455, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "send_eof", "line": 731, "message": "> EOF", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43fcbef380>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018597.2575283, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "discard", "line": 621, "message": "< EOF", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43fcbef380>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018597.2583995, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CLOSED", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43fcbef380>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018597.669132, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018597.6702743, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018597.669132, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "pid": 48, "job_id": "AJ_K6GqJbU36e5e", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018597.6711595, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 61, "message": "Salon AI Application shutdown complete", "event": "application_shutdown", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018597.6702743, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "pid": 48, "job_id": "AJ_K6GqJbU36e5e", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018597.6723568, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_close_http_ctx", "line": 57, "message": "http_session(): closing the httpclient ctx", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018597.6734328, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_new_session", "line": 20, "message": "http_session(): creating a new httpclient ctx", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018597.6711595, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 61, "message": "Salon AI Application shutdown complete", "event": "application_shutdown", "component": "database", "status": "success", "pid": 48, "job_id": "AJ_K6GqJbU36e5e", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018637.3387098, "level": "INFO", "logger": "livekit.agents", "module": "worker", "function": "_answer_availability", "line": 870, "message": "received job request", "job_id": "AJ_2ie2ps8FfbqR", "dispatch_id": "", "room_name": "playground-ygm5-YMDQ", "agent_name": "", "resuming": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018637.7562928, "level": "DEBUG", "logger": "tortoise", "module": "__init__", "function": "init", "line": 505, "message": "Tortoise-ORM startup\n    connections: {'default': {'engine': 'tortoise.backends.asyncpg', 'credentials': {'host': 'voice-bot-db-1.cjoiyo4uqmpi.ap-south-1.rds.amazonaws.com', 'port': 5432, 'user': 'postgres', 'password': 'uhmpBp***', 'database': 'postgres', 'minsize': 1, 'maxsize': 10, 'command_timeout': 60}}}\n    apps: {'models': {'models': ['models.customer', 'models.livekit', 'models.staff', 'models.service', 'models.appointment', 'models.recording', 'aerich.models'], 'default_connection': 'default'}}", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018637.8285465, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018637.8298361, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 42, "message": "Salon AI Application started successfully", "event": "application_startup", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018637.8285465, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "initialize", "line": 14, "message": "Database connection established", "pid": 46, "job_id": "AJ_2ie2ps8FfbqR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018637.8298361, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "startup", "line": 42, "message": "Salon AI Application started successfully", "event": "application_startup", "component": "database", "status": "success", "pid": 46, "job_id": "AJ_2ie2ps8FfbqR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018637.8364806, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit_api::signal_client::signal_stream:106:livekit_api::signal_client::signal_stream - connecting to wss://salon-dev-z1d1tpn4.livekit.cloud/rtc?sdk=python&protocol=16&auto_subscribe=1&adaptive_stream=0&version=1.0.12&access_token=...", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018637.8448262, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 167, "message": "initializing process", "pid": 168, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018638.1151166, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::anchors:150:rustls::anchors - add_parsable_certificates processed 142 valid and 0 invalid certs", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018638.1162224, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tokio_tungstenite::tls::encryption::rustls:103:tokio_tungstenite::tls::encryption::rustls - Added 142/142 native root certificates (ignored 0)", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018638.1170776, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:73:rustls::client::hs - No cached session for DnsName(\"salon-dev-z1d1tpn4.livekit.cloud\")", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018638.1181395, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:132:rustls::client::hs - Not resuming any session", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018638.3569982, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:615:rustls::client::hs - Using ciphersuite TLS13_AES_128_GCM_SHA256", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018638.3581347, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:142:rustls::client::tls13 - Not resuming", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018638.3590481, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::tls13:381:rustls::client::tls13 - TLS1.3 encrypted extensions: []", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018638.3601446, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "rustls::client::hs:472:rustls::client::hs - ALPN protocol is None", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018638.6020644, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::handshake::client:95:tungstenite::handshake::client - Client handshake done.", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018638.8744545, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "initialize", "line": 184, "message": "process initialized", "pid": 168, "elapsed_time": 1.03, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018638.87444, "level": "DEBUG", "logger": "asyncio", "module": "selector_events", "function": "__init__", "line": 64, "message": "Using selector: EpollSelector", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018645.2268207, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "on_attached", "line": 65, "message": "input stream attached", "participant": null, "source": "SOURCE_UNKNOWN", "accepted_sources": ["SOURCE_MICROPHONE"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018645.4884233, "level": "DEBUG", "logger": "livekit.plugins.google", "module": "realtime_api", "function": "_main_task", "line": 588, "message": "connecting to Gemini Realtime API...", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018646.6905506, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "_forward_task", "line": 141, "message": "start reading stream", "participant": "identity-hBCX", "source": "SOURCE_MICROPHONE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018646.6953168, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "start", "line": 546, "message": "using audio io: `RoomIO` -> `AgentSession` -> `TranscriptSynchronizer` -> `RoomIO`", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018646.6962678, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "start", "line": 552, "message": "using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `RoomIO`", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018646.7008502, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "_forward_task", "line": 148, "message": "stream closed", "participant": "identity-hBCX", "source": "SOURCE_MICROPHONE", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018646.71625, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CONNECTING", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018646.82918, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 310, "message": "> GET //ws/google.ai.generativelanguage.v1beta.GenerativeService.BidiGenerateContent HTTP/1.1", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43ff1a7ef0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018646.8302357, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Host: generativelanguage.googleapis.com", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43ff1a7ef0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018646.831299, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Upgrade: websocket", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43ff1a7ef0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018646.8322563, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Connection: Upgrade", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43ff1a7ef0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018646.8332808, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Key: bLu9l+s+1iMzdWhNx+RZDw==", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43ff1a7ef0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018646.8343084, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Version: 13", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43ff1a7ef0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018646.835079, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43ff1a7ef0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018646.8359618, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> Content-Type: application/json", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43ff1a7ef0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018646.8372748, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-key: AIzaSyBC243sIVBhUjf6LnkE1oP2DfCucwr1D6M", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43ff1a7ef0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018646.8381808, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> user-agent: google-genai-sdk/1.29.0 gl-python/3.12.11", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43ff1a7ef0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018646.8389978, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> x-goog-api-client: google-genai-sdk/1.29.0 gl-python/3.12.11", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43ff1a7ef0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018646.8397653, "level": "DEBUG", "logger": "websockets.client", "module": "client", "function": "send_request", "line": 312, "message": "> User-Agent: Python/3.12 websockets/13.1", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43ff1a7ef0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018646.927947, "level": "DEBUG", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "tungstenite::protocol:666:tungstenite::protocol - Received close frame: None", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018646.9337673, "level": "INFO", "logger": "livekit", "module": "_ffi_client", "function": "ffi_event_callback", "line": 167, "message": "livekit::room:1388:livekit::room - disconnected from room with reason: ClientInitiated", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018646.937838, "level": "DEBUG", "logger": "livekit.agents", "module": "job_proc_lazy_main", "function": "_run_job_task", "line": 278, "message": "shutting down job task", "reason": "", "user_initiated": false, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018646.939117, "level": "INFO", "logger": "livekit.agents", "module": "supervised_proc", "function": "_read_ipc_task", "line": 318, "message": "process exiting", "reason": "", "pid": 46, "job_id": "AJ_2ie2ps8FfbqR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018646.9417994, "level": "DEBUG", "logger": "livekit.agents", "module": "_input", "function": "on_detached", "line": 78, "message": "input stream detached", "participant": "identity-hBCX", "source": "SOURCE_MICROPHONE", "accepted_sources": ["SOURCE_MICROPHONE"], "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018646.9457462, "level": "DEBUG", "logger": "livekit.agents", "module": "agent_session", "function": "_aclose_impl", "line": 654, "message": "session closed", "reason": "job_shutdown", "error": null, "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018648.2232256, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "send_eof", "line": 731, "message": "> EOF", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43ff1a7ef0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018648.22432, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "discard", "line": 621, "message": "< EOF", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43ff1a7ef0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018648.2252195, "level": "DEBUG", "logger": "websockets.client", "module": "protocol", "function": "state", "line": 169, "message": "= connection is CLOSED", "websocket": "<websockets.asyncio.client.ClientConnection object at 0x7f43ff1a7ef0>", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018649.0624845, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018649.063492, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018649.0624845, "level": "INFO", "logger": "tortoise", "module": "__init__", "function": "close_connections", "line": 570, "message": "Tortoise-ORM shutdown", "pid": 46, "job_id": "AJ_2ie2ps8FfbqR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018649.0643277, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 61, "message": "Salon AI Application shutdown complete", "event": "application_shutdown", "component": "database", "status": "success", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018649.063492, "level": "INFO", "logger": "database.connection", "module": "connection", "function": "close", "line": 24, "message": "Database connections closed", "pid": 46, "job_id": "AJ_2ie2ps8FfbqR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018649.0651264, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_close_http_ctx", "line": 57, "message": "http_session(): closing the httpclient ctx", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018649.0643277, "level": "INFO", "logger": "salon_ai.main", "module": "main", "function": "shutdown", "line": 61, "message": "Salon AI Application shutdown complete", "event": "application_shutdown", "component": "database", "status": "success", "pid": 46, "job_id": "AJ_2ie2ps8FfbqR", "service": "salon-ai-voice-agent", "environment": "development"}
{"timestamp": 1755018649.0660255, "level": "DEBUG", "logger": "livekit.agents", "module": "http_context", "function": "_new_session", "line": 20, "message": "http_session(): creating a new httpclient ctx", "service": "salon-ai-voice-agent", "environment": "development"}
