import uuid, asyncio, redis, time
import warnings

from livekit.agents import JobContext, WorkerOptions, cli, RoomInputOptions
from livekit.agents.voice import AgentSession
from livekit.rtc import participant
from config.logging import setup_logging, get_logger
from livekit_handlers.recording import RoomRecodingAndTranscript
from livekit.plugins import noise_cancellation

from livekit import rtc
from agent.assistant import Assistant
from database.connection import DatabaseManager
from models import Customer
from shared_types.context_variables import ContextVariables
from config.settings import settings
from livekit import api
from helpers import redis_helper
from datetime import timedelta
from dotenv import load_dotenv
from utils.prometheus_metric_collector import PrometheusExporter


setup_logging()
logger = get_logger('main')

# Initialize Prometheus metrics server globally
prometheus_exporter = PrometheusExporter()
prometheus_exporter.start_metrics_server()


class Database:
    """Main application class"""

    def __init__(self):
        self.db_manager = DatabaseManager()

    async def startup(self):
        """Application startup"""
        try:
            await self.db_manager.initialize()
            logger.info("Salon AI Application started successfully", extra={
                "event": "application_startup",
                "component": "database",
                "status": "success"
            })
        except Exception as e:
            logger.error("Failed to start application", extra={
                "event": "application_startup",
                "component": "database",
                "status": "error",
                "error": str(e),
                "error_type": type(e).__name__
            })
            raise

    async def shutdown(self):
        """Application shutdown"""
        try:
            await self.db_manager.close()
            logger.info("Salon AI Application shutdown complete", extra={
                "event": "application_shutdown",
                "component": "database",
                "status": "success"
            })
        except Exception as e:
            logger.error("Error during shutdown", extra={
                "event": "application_shutdown",
                "component": "database",
                "status": "error",
                "error": str(e),
                "error_type": type(e).__name__
            })


async def delete_room(room_name: str):
    print("DELETING")
    async with api.LiveKitAPI() as lkapi:
        await lkapi.room.delete_room(
            api.DeleteRoomRequest(
                room=room_name,
            )
        )


async def entrypoint(ctx: JobContext):
    try:
        db = Database()
        await db.startup()

        # Connect to room first
        await ctx.connect()
        
        # Initialize session after connection
        session = AgentSession()

        # LiveKitAPI will be used with async context manager when needed
        rclient = redis.Redis(
            host = settings.REDIS_HOST, 
            port = settings.REDIS_PORT, 
            db=settings.REDIS_DB,
            decode_responses=True,
            username=settings.REDIS_USERNAME,
            password=settings.REDIS_PASS
        )

        context_variables: ContextVariables = {
            "appointment_id": uuid.uuid4(),
            "recording_id": uuid.uuid4(),
        }

        participant = await ctx.wait_for_participant()

        # Record call start time when participant connects
        call_start_time = time.time()
        # await recording_and_transcript.turn_on_background_audio()
        context_variables["participant_id"] = participant.identity

        if participant.kind == rtc.ParticipantKind.PARTICIPANT_KIND_SIP:
            context_variables["phone_number"] = participant.attributes[
                "sip.phoneNumber"
            ]
        elif participant.kind == rtc.ParticipantKind.PARTICIPANT_KIND_STANDARD:
            context_variables["phone_number"] = "+14434231722"
        else:
            context_variables["phone_number"] = participant.attributes["phone_number"]

        if context_variables.get("phone_number"):
            customer_dict = rclient.hgetall(f'customer{context_variables["phone_number"]}') 
            if customer_dict:
                restored_data = redis_helper.restore_from_redis(customer_dict)
                customer = Customer()
                for key, value in restored_data.items():
                    setattr(customer, key, value)
                context_variables["customer"] = customer
            else:
                context_variables["customer"] = await Customer.find_by_phone(
                    phone_number=context_variables["phone_number"]
                )

            if context_variables["customer"]:
                customer_dict = redis_helper.prepare_for_redis(
                    context_variables["customer"].__dict__
                )

                key_name = f'customer{context_variables["phone_number"]}'
                rclient.hset(name=key_name, mapping=customer_dict)
                # rclient.expire(name=key_name, time=int(timedelta(minutes=30).total_seconds()))

        ctx.room.on(
            "participant_disconnected",
            lambda _: asyncio.create_task(delete_room(ctx.room.name)),
        )

        assistant = await Assistant.create(context_vars=context_variables, jobCtx=ctx, prometheus_exporter=prometheus_exporter)

        async def on_shutdown():
            total_time = time.time() - call_start_time
            duration_minutes = total_time / 60.0  # Convert seconds to minutes
            # await recording_and_transcript.write_transcript(
            #     is_appointment_created=assistant.is_appointment_created,
            #     customer_id=context_variables["customer"].id,
            #     duration=duration_minutes
            # )
            await assistant.log_usage_summary()
            await delete_room(ctx.room.name)
            await db.shutdown()

        ctx.add_shutdown_callback(on_shutdown)

        await session.start(
            agent=assistant,
            room=ctx.room,
            room_input_options=RoomInputOptions(
                noise_cancellation=noise_cancellation.BVCTelephony(),
            ),
        )

    except Exception as e:
        print("Error during initialization")
        raise e


if __name__ == "__main__":
    load_dotenv()
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            shutdown_process_timeout=100,
            # agent_name="inbound-agent",
        )
    )
